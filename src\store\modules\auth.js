import Cookies from 'js-cookie'
import { login<PERSON>pi, qrC<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/apis/auth'
import { stopRefreshTokenInterval } from '@/store'

export default {
  namespaced: true,
  state: {
    user: null,
    authType: Cookies.get('authType') || null,
    isAuthenticated: false,
    loading: false
  },
  mutations: {
    SET_USER(state, user) {
      state.user = user
    },
    SET_AUTH_TYPE(state, type) {
      state.authType = type
      Cookies.set('authType', type)
    },
    SET_AUTHENTICATED(state, status) {
      state.isAuthenticated = status
    },
    CLEAR_AUTH_TYPE(state) {
      state.authType = null
      Cookies.remove('authType')
    },
    SET_LOADING(state, status) {
      state.loading = status // 设置加载状态
    }
  },
  getters: {
    getUser: (state) => state.user,
  },
  actions: {
    async login({ commit }, credentials) {
      try {
        commit('SET_LOADING', true)
        const res = await login<PERSON>pi(credentials)
        if (res && res.data) {
          Cookies.set('AMS-SIMPLE-ADMIN-TRADE-TOKEN', res.data.access_token);
          Cookies.set('authType', res.data.userType)
          localStorage.setItem('isPasswordModify', res.data.isPasswordModify);
          commit('SET_AUTH_TYPE', res.data.userType)
          commit('SET_USER', res.data)
          commit('SET_AUTHENTICATED', true)
          return true
        } else {
          console.error('Invalid response from server:', res)
        }
      } catch (error) {
        console.error('Login failed:', error)
        this.$message.error('登录失败，请检查用户名和密码') // 使用 Element UI 的消息提示组件
      } finally {
        commit('SET_LOADING', false) // 设置加载状态为 false
      }
    },
    async qrLogin({ commit }, credentials) {
      try {
        commit('SET_LOADING', true)
        const res = await qrCodeLoginApi(credentials)
        if (res && res.data) {
          Cookies.set('AMS-SIMPLE-ADMIN-TRADE-TOKEN', res.data.access_token);
          Cookies.set('authType', res.data.userType)
          localStorage.setItem('isPasswordModify', res.data.isPasswordModify);
          commit('SET_AUTH_TYPE', res.data.userType)
          commit('SET_USER', res.data)
          commit('SET_AUTHENTICATED', true)
          return true
        } else {
          console.error('Invalid response from server:', res)
        }
      } catch (error) {
        console.error('Login failed:', error)
        this.$message.error('登录失败，请检查用户名和密码') // 使用 Element UI 的消息提示组件
      } finally {
        commit('SET_LOADING', false) // 设置加载状态为 false
      }
    },
    logout({ commit }) {
      Cookies.remove('AMS-SIMPLE-ADMIN-TRADE-TOKEN')
      Cookies.remove('authType')
      commit('CLEAR_AUTH_TYPE')
      // 清除用户信息
      commit('SET_USER', null)
      // 可以在这里添加其他清理操作，如重置其他状态
      commit('SET_AUTHENTICATED', false)
      stopRefreshTokenInterval()
    }
  }
}
