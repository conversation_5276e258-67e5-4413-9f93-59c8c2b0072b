/**
 * 权限管理工具函数
 */

/**
 * 检查用户是否有访问路由的权限
 * @param {Object} route - 路由对象
 * @param {Object} user - 用户信息
 * @param {string} authType - 用户认证类型
 * @returns {boolean} - 是否有权限
 */
export function hasPermission(route, user, authType) {
  // 如果路由没有设置权限要求，则允许访问
  if (!route.meta) {
    return true
  }

  const { roles } = route.meta

  // 如果没有设置角色和认证类型限制，则允许访问
  if (!roles) {
    return true
  }

  // 检查用户角色权限
  if (roles && roles.length > 0) {
    const userRole = user?.role || user?.userType || authType
    if (userRole && roles.includes(userRole)) {
      return true
    }
  }

  return false
}

/**
 * 过滤用户可访问的路由
 * @param {Array} routes - 路由数组
 * @param {Object} user - 用户信息
 * @param {string} authType - 用户认证类型
 * @returns {Array} - 过滤后的路由数组
 */
export function filterRoutes(routes, user, authType) {
  const filteredRoutes = []

  routes.forEach((route) => {
    // 创建路由副本，避免修改原始路由
    const routeCopy = { ...route }

    // 检查当前路由权限
    if (hasPermission(route, user, authType)) {
      // 如果有子路由，递归过滤子路由
      if (route.children && route.children.length > 0) {
        routeCopy.children = filterRoutes(route.children, user, authType)

        // 如果过滤后没有子路由，且当前路由需要子路由才能显示，则不显示该路由
        if (routeCopy.children.length === 0 && route.alwaysShow !== true) {
          // 检查是否有默认子路由（path为空的子路由）
          const hasDefaultChild = route.children.some((child) => child.path === '')
          if (!hasDefaultChild) {
            return // 跳过这个路由
          }
        }
      }

      filteredRoutes.push(routeCopy)
    }
  })

  return filteredRoutes
}

/**
 * 获取用户可访问的菜单路由
 * @param {Array} routes - 所有路由
 * @param {Object} user - 用户信息
 * @param {string} authType - 用户认证类型
 * @returns {Array} - 菜单路由数组
 */
export function getAccessibleMenuRoutes(routes, user, authType) {
  // 过滤掉不需要在菜单中显示的路由
  const menuRoutes = routes.filter((route) => {
    return !route.hidden && route.component && route.path !== '/login' && route.path !== '*'
  })

  // 根据权限过滤路由
  const filteredRoutes = filterRoutes(menuRoutes, user, authType)

  // 转换为菜单格式，提取主要信息
  return filteredRoutes.map((route) => {
    const menuItem = {
      path: route.path,
      name: route.name,
      meta: route.meta,
      component: route.component
    }

    // 如果有子路由，处理子路由
    if (route.children && route.children.length > 0) {
      // 对于有子路由的情况，通常菜单显示第一个子路由的路径
      const firstChild = route.children[0]
      if (firstChild && firstChild.path === '') {
        // 如果第一个子路由是默认路由（path为空），使用父路由路径
        menuItem.path = route.path
      } else if (firstChild) {
        // 否则使用完整的子路由路径
        menuItem.path = route.path + '/' + firstChild.path
      }
    }

    return menuItem
  })
}

/**
 * 检查用户是否可以访问指定路径
 * @param {string} path - 路由路径
 * @param {Array} routes - 所有路由
 * @param {Object} user - 用户信息
 * @param {string} authType - 用户认证类型
 * @returns {boolean} - 是否可以访问
 */
export function canAccessPath(path, routes, user, authType) {
  // 递归查找路由
  function findRoute(routes, targetPath) {
    for (const route of routes) {
      if (route.path === targetPath) {
        return route
      }
      if (route.children && route.children.length > 0) {
        const found = findRoute(route.children, targetPath)
        if (found) return found
      }
    }
    return null
  }

  const route = findRoute(routes, path)
  if (!route) {
    return false
  }

  return hasPermission(route, user, authType)
}

/**
 * 根据用户权限获取默认首页路径
 * @param {Object} user - 用户信息
 * @param {string} authType - 用户认证类型
 * @returns {string} - 默认首页路径
 */
export function getDefaultHomePath(user, authType) {
  // 根据用户类型返回不同的默认首页
  switch (authType) {
    case '101':
      return '/enterpriseInfoMaintenance'
    case '102':
      return '/stamp-declaration/list'
    case '103':
      return '/reconsideration-declaration/list'
    case '104':
      return '/trade-verification/list'
    case '105':
      return '/infomation-approval/list'
    case '106':
      return '/infomation-approval/list'
    case '107':
      return '/reconsideration-declaration/list'
    case '108':
      return '/infomation-review/list'
    default:
      return '/enterpriseInfoMaintenance'
  }
}

