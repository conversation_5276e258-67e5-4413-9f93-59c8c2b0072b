<template>
    <div class="basic-info">
      <el-form label-position="top" :model="editableData" :rules="rules" ref="editableData">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="info-item">
              <div class="label"><span v-if="isEdit" class="required-star">*</span>企业名称：</div>
              <el-form-item prop="companyName">
                <div v-if="!isEdit" class="value">{{ enterpriseData.companyName || "/" }}</div>
                <div v-else class="value">
                    <el-input v-model="editableData.companyName" placeholder="请输入企业名称"></el-input> 
                </div>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="label"><span v-if="isEdit" class="required-star">*</span>统一社会信用代码：</div>
              <el-form-item prop="creditCode">
                <div v-if="!isEdit" class="value">{{ enterpriseData.creditCode || "/" }}</div>
                <div v-else class="value">
                    <el-input v-model="editableData.creditCode" placeholder="请输入统一社会信用代码"></el-input>
                </div>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="label"><span v-if="isEdit" class="required-star">*</span>企业类型：</div>
              <el-form-item prop="companyType">
                <div v-if="!isEdit" class="value">{{ enterpriseData.companyTypeName || "/" }}</div>
                <div v-else class="value">
                    <el-select v-model="editableData.companyType" style="width: 100%;" placeholder="请选择企业类型">
                        <el-option
                        v-for="item in enterpriseTypeList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                        </el-option>
                    </el-select>
                </div>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="label"><span v-if="isEdit" class="required-star">*</span>法定代表人：</div>
              <el-form-item prop="legalRepresentative">
                <div v-if="!isEdit" class="value">{{ enterpriseData.legalRepresentative || "/" }}</div>
                <div v-else class="value">
                    <el-input v-model="editableData.legalRepresentative" placeholder="请输入法定代表人"></el-input>
                </div>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="info-item">
              <div class="label"><span v-if="isEdit" class="required-star">*</span>注册资本：</div>
              <el-form-item prop="registeredCapital">
                <div v-if="!isEdit" class="value">{{ enterpriseData.registeredCapital || "/" }}</div>
                <div v-else class="value">
                    <el-input v-model="editableData.registeredCapital" placeholder="请输入注册资本">
                        <template slot="append">万元</template>
                    </el-input>
                </div>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="label">实缴资本：</div>
              <el-form-item prop="actualCapital">
                <div v-if="!isEdit" class="value">{{ enterpriseData.actualCapital || "/" }}</div>
                <div v-else class="value">
                    <el-input v-model="editableData.actualCapital" placeholder="请输入实缴资本">
                        <template slot="append">万元</template>
                    </el-input>
                </div>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="label"><span v-if="isEdit" class="required-star">*</span>成立日期：</div>
              <el-form-item prop="establishmentDate">
                <div v-if="!isEdit" class="value">{{ enterpriseData.establishmentDate || "/" }}</div>
                <div v-else class="value">
                    <el-date-picker
                        v-model="editableData.establishmentDate"
                        type="date"
                        style="width: 100%;"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                        placeholder="选择日期">
                    </el-date-picker>
                </div>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="label">营业期限：</div>
              <el-form-item prop="operatingPeriod">
                <div v-if="!isEdit" class="value">{{ enterpriseData.operatingPeriod || "/" }}</div>
                <div v-else class="value">
                    <el-date-picker
                        v-model="editableData.operatingPeriod"
                        type="daterange"
                        style="width: 100%;"
                        range-separator="-"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd">
                    </el-date-picker>
                </div>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="info-item">
              <div class="label">登记机关：</div>
              <el-form-item prop="registrationAuthority">
                <div v-if="!isEdit" class="value">{{ enterpriseData.registrationAuthority || "/" }}</div>
                <div v-else class="value">
                    <el-input v-model="editableData.registrationAuthority" placeholder="请输入登记机关"></el-input>
                </div>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="label">核准日期：</div>
              <el-form-item prop="approvalDate">
                <div v-if="!isEdit" class="value">{{ enterpriseData.approvalDate || "/" }}</div>
                <div v-else class="value">
                    <el-date-picker
                        v-model="editableData.approvalDate"
                        type="date"
                        style="width: 100%;"
                        placeholder="选择日期"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd">
                    </el-date-picker>
                </div>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="label"><span v-if="isEdit" class="required-star">*</span>所属行业：</div>
              <el-form-item prop="industry">
                <div v-if="!isEdit" class="value">{{ enterpriseData.industry || "/" }}</div>
                <div v-else class="value">
                    <el-input v-model="editableData.industry" placeholder="请输入所属行业"></el-input>
                </div>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <div class="label"><span v-if="isEdit" class="required-star">*</span>经营范围：</div>
              <el-form-item prop="scopeItem">
                <div v-if="!isEdit" class="value">
                    <div style="margin-bottom: 10px;">
                        {{ enterpriseData.scopeItem || "/" }}
                    </div>
                </div>
                <div v-else class="value">
                    <el-input
                        type="textarea"
                        :rows="6"
                        placeholder="请输入内容"
                        v-model="editableData.scopeItem">
                    </el-input>
                </div>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </template>
  
  <script>
  import { dictEnterpriseType } from '@/apis/enum'
  export default {
    props: {
      enterpriseData: {
        type: Object,
        required: true,
      },
      basicInfoFormData: {
        type: Object,
        required: true,
        },
      isEdit: {
        type: Boolean,
        default: false,
      },
    },
    data() {
        return {
          editableData: {},
          enterpriseTypeList:[],
          rules: {
            companyName: [
              { required: true, message: '请输入公司名称', trigger: 'blur' }
            ],
            creditCode: [
              { required: true, message: '请输入信用代码', trigger: 'blur' },
              {
                pattern: /^[A-Za-z0-9]{18}$/,
                message: '统一社会信用代码应为18位字母和数字组合',
                trigger: 'blur',
              },
            ],
            companyType: [
              { required: true, message: '请选择公司类型', trigger: 'change' }
            ],
            legalRepresentative: [
              { required: true, message: '请输入法人代表', trigger: 'blur' }
            ],
            registeredCapital: [
              { required: true, message: '请输入注册资金', trigger: 'blur' },
              { pattern: /^[0-9]+(\.[0-9]{1,2})?$/, message: '注册资金必须为数字，最多两位小数', trigger: 'blur' }
            ],
            establishmentDate: [
              { required: true, message: '请选择成立日期', trigger: 'change' }
            ],
            industry: [
              { required: true, message: '请输入所属行业', trigger: 'blur' }
            ],
            scopeItem: [
              { required: true, message: '请输入经营范围', trigger: 'blur' }
            ],
          }
      };
    },
    watch: {
      basicInfoFormData: {
        handler(newVal) {
          this.editableData = { ...newVal };
        },
        deep: true,
        immediate: true
        },
    },
    async mounted() {
      this.editableData = { ...this.basicInfoFormData };
      const res = await dictEnterpriseType();
      this.enterpriseTypeList = res.data.details
    },
    methods: {
      getFormData() {
        return this.editableData;
      },
        handleSave() {
            this.$emit('save', this.editableData);
        },
    },
  };
  </script>
  
  <style scoped>
  .basic-info{
    margin-top: 10px;
  }
  .basic-info .info-item {
    margin-bottom: 30px;
  }
  
  .basic-info .label {
    color: #515A6E;
    font-weight: 400;
    font-size: 14px;
  }
  
  .basic-info .value {
    margin-top: 10px;
    color: #17233D;
    font-weight: 400;
    font-size: 14px;
  }
  .required-star{
    color: red;
    margin-right: 5px;
  }
  </style>
  <style lang="scss" scoped>
/deep/.el-form-item__content {
  line-height: normal;
}
// /deep/ .el-form-item__label{
//   padding: 0;
// }
</style>