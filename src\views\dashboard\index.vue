<template>
  <div class="dashboard-container">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card shadow="hover" class="dashboard-card">
          <div slot="header" class="clearfix">
            <span>贸易总数</span>
          </div>
          <div class="card-panel">
            <div class="card-panel-icon-wrapper">
              <i class="el-icon-s-goods card-panel-icon"></i>
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">总数</div>
              <div class="card-panel-num">128</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="dashboard-card">
          <div slot="header" class="clearfix">
            <span>待处理订单</span>
          </div>
          <div class="card-panel">
            <div class="card-panel-icon-wrapper">
              <i class="el-icon-s-order card-panel-icon"></i>
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">待处理</div>
              <div class="card-panel-num">24</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="dashboard-card">
          <div slot="header" class="clearfix">
            <span>本月收入</span>
          </div>
          <div class="card-panel">
            <div class="card-panel-icon-wrapper">
              <i class="el-icon-money card-panel-icon"></i>
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">收入</div>
              <div class="card-panel-num">¥ 568,000</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="dashboard-card">
          <div slot="header" class="clearfix">
            <span>用户数量</span>
          </div>
          <div class="card-panel">
            <div class="card-panel-icon-wrapper">
              <i class="el-icon-user card-panel-icon"></i>
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">用户</div>
              <div class="card-panel-num">42</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card shadow="hover" class="dashboard-card">
          <div slot="header" class="clearfix">
            <span>最近交易</span>
          </div>
          <el-table :data="recentTrades" style="width: 100%">
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="name" label="名称" />
            <el-table-column prop="date" label="日期" width="120" />
            <el-table-column prop="amount" label="金额" width="120" />
          </el-table>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card shadow="hover" class="dashboard-card">
          <div slot="header" class="clearfix">
            <span>待办事项</span>
          </div>
          <el-table :data="todoList" style="width: 100%">
            <el-table-column prop="title" label="标题" />
            <el-table-column prop="date" label="截止日期" width="120" />
            <el-table-column prop="status" label="状态" width="100">
              <template slot-scope="scope">
                <el-tag :type="scope.row.status === '紧急' ? 'danger' : 'primary'">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Dashboard',
  data() {
    return {
      recentTrades: [
        { id: 'TR001', name: '美国进口贸易', date: '2023-05-15', amount: '¥120,000' },
        { id: 'TR002', name: '欧洲出口贸易', date: '2023-05-12', amount: '¥85,000' },
        { id: 'TR003', name: '亚洲区域贸易', date: '2023-05-10', amount: '¥65,000' },
        { id: 'TR004', name: '非洲进口贸易', date: '2023-05-08', amount: '¥45,000' }
      ],
      todoList: [
        { title: '完成月度报表', date: '2023-05-20', status: '紧急' },
        { title: '审核贸易合同', date: '2023-05-22', status: '普通' },
        { title: '客户会议', date: '2023-05-25', status: '普通' },
        { title: '系统升级', date: '2023-05-18', status: '紧急' }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
}

.dashboard-card {
  margin-bottom: 20px;
}

.card-panel {
  display: flex;
  align-items: center;
  height: 108px;
}

.card-panel-icon-wrapper {
  float: left;
  margin: 14px 0 0 14px;
  padding: 16px;
  transition: all 0.38s ease-out;
  border-radius: 6px;
  background-color: #ecf0f1;
}

.card-panel-icon {
  float: left;
  font-size: 48px;
}

.card-panel-description {
  float: right;
  font-weight: bold;
  margin: 26px 0 0 15px;
}

.card-panel-text {
  line-height: 18px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 16px;
  margin-bottom: 12px;
}

.card-panel-num {
  font-size: 20px;
}
</style>
