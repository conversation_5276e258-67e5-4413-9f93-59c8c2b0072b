/**
 * 处理文件下载
 * @param {Blob} blob - 二进制数据
 * @param {string} [filename] - 文件名，如果未提供将使用默认名称
 * @param {string} [defaultName='document.pdf'] - 默认文件名
 */
export function downloadFile(blob, filename, defaultName = 'document.pdf') {
  if (!(blob instanceof Blob)) {
    console.error('下载失败：不是有效的Blob对象', blob)
    return false
  }

  try {
    // 创建Blob URL
    const url = window.URL.createObjectURL(blob)

    // 创建临时下载链接
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', filename || defaultName)
    document.body.appendChild(link)

    // 触发下载
    link.click()

    // 清理
    setTimeout(() => {
      window.URL.revokeObjectURL(url)
      document.body.removeChild(link)
    }, 100)

    return true
  } catch (error) {
    console.error('下载文件时出错:', error)
    return false
  }
}