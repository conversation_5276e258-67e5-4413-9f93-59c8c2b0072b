<template>
  <div>
    <h3>商业银行维护</h3>
    <div class="enterprise-detail">
        <div class="enterprise-detail-title">
            <div>舟山远洋贸易有限公司</div>
            <el-button v-if="!isEdit" type="primary" @click="handleEdit" style="float: right;">
                编辑
            </el-button>
            <div v-if="isEdit" style="float: right;">
                <el-button @click="handleClose" style="margin-right: 10px;">
                    取消
                </el-button>
                <el-button type="primary" @click="handleSave" >
                    保存
                </el-button>
            </div>
        </div>
      <!-- 标签页 -->
      <el-tabs v-model="activeName" class="enterprise-detail-tabs">
        <el-tab-pane label="基本信息" name="basicInfo">
          <BasicInfo :key="'basic-info-' + isEdit" :bankInfo="bankInfo" :basicInfoFormData="basicInfoFormData" :isEdit="isEdit" ref="basicInfoForm" />
        </el-tab-pane>
        <el-tab-pane label="联系信息" name="contactInfo">
          <ContactInfo :key="'contact-info-' + isEdit" :bankInfo="bankInfo" :contactInfoFormData="contactInfoFormData" :isEdit="isEdit" ref="contactInfoForm"  />
        </el-tab-pane>
        <el-tab-pane label="业务信息" name="businessInfo">
          <BusinessInfo :key="'business-info-' + isEdit" :bankInfo="bankInfo" :businessInfoFormData="businessInfoFormData" :isEdit="isEdit" ref="businessInfoForm" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
  
  <script>
  import BasicInfo from './components/BasicInfo.vue';
  import ContactInfo from './components/ContactInfo.vue';
  import BusinessInfo from './components/BusinessInfo.vue';
  import { createCompanyApi,getCompanyDetailApi,updateCompanyDetailApi } from '@/apis/index';
  
  export default {
    components: {
      BasicInfo,
      ContactInfo,
      BusinessInfo,
    },
    data() {
        return {
        activeName: 'basicInfo',
        isEdit: false,
          isAdd: false,
          basicInfoFormData: {
            companyName: '',
            financialInstitutionCode: '',
            companyType: '',
            location: '',
            establishmentDate: '',
            operatingStatus: '',
            financialLicenseNumber: '',
            businessLicenseNumber: '',
          },
          contactInfoFormData: {
            registrationAddress: '',
            officeAddress: '',
            contactPerson: '',
            contactPhone: '',
            email: '',
            postalCode: '',
          },
          businessInfoFormData: {
            taxpayerQualification :'',
            importExportCode :'',
            customsCode :'',
            openingBank :'',
            bankAccount :'',
            annualRevenue :'',
            employeeCount :'',
          },
        bankInfo: {
          companyName: '',
          financialInstitutionCode: '',
          companyType: '',
          location: '',
          establishmentDate: '',
          operatingStatus: '',
          financialLicenseNumber: '',
          businessLicenseNumber: '',
          registrationAddress: '',
        officeAddress: '',
        contactPerson: '',
            contactPhone: '',
            email: '',
          postalCode: '',
          mainBusiness: '',
        swiftCode: '',
        clearingBankNumber: '',
        foreignExchangeQualification: '',
        crossBorderRmbQualification: ''
        }
      };
    },
    async mounted() {
    console.log('this.$route.query.isEdit', this.$route.query.isAdd, this.$route.query.isEdit);
    if (this.$route.query.isEdit) {
      this.isEdit = true
    }
    if (this.$route.query.isAdd) {
      this.isAdd = true
      this.isEdit = true
    }
    if (this.$route.query.id) {
      this.getCompanyDetail()
    }
  },
    methods: {
        handleEdit() {
        this.isEdit = true;
        console.log('编辑企业信息');
      },
      getCompanyDetail() {
        getCompanyDetailApi(this.$route.query.id).then(res => {
          if (res.code == 200) {
            this.bankInfo = res.data;
            Object.keys(this.basicInfoFormData).forEach(key => {
              if (Object.prototype.hasOwnProperty.call(res.data, key)) {
                this.basicInfoFormData[key] = res.data[key];
              }
            });
            Object.keys(this.contactInfoFormData).forEach(key => {
              if (Object.prototype.hasOwnProperty.call(res.data, key)) {
                this.contactInfoFormData[key] = res.data[key];
              }
            });
            Object.keys(this.businessInfoFormData).forEach(key => {
              if (Object.prototype.hasOwnProperty.call(res.data, key)) {
                this.businessInfoFormData[key] = res.data[key];
              }
            });
          }
        });
      },
        async handleSave() {
          const formRefs = [
            this.$refs.basicInfoForm,
            this.$refs.contactInfoForm,
            this.$refs.businessInfoForm,
          ];
          const basicInfoFormData = this.$refs.basicInfoForm.getFormData();
          const contactInfoFormData = this.$refs.contactInfoForm.getFormData();
          const businessInfoFormData = this.$refs.businessInfoForm.getFormData();
          let isValid = true;
          for (const ref of formRefs) {
            if (ref && ref.$refs.editableData) {
              try {
                await ref.$refs.editableData.validate();
              } catch (error) {
                isValid = false;
                break;
              }
            }
          }
          const formData = { ...basicInfoFormData, ...contactInfoFormData, ...businessInfoFormData }
          formData.organRoleList = [104]
          if (isValid) {
            console.log('所有表单校验通过，准备提交:',this.isAdd,formData);
            // 调用保存接口
            if (this.isAdd) {
              createCompanyApi(formData).then(res => {
                console.log('提交成功:', res);
                this.isEdit = false;
                this.$router.replace('/bankMaintenance')
              });
            }
            if (this.isEdit && !this.isAdd) {
              formData.id = this.$route.query.id
              updateCompanyDetailApi(formData).then(res => {
                console.log('提交成功:', res);
                if (res.code === 200) {
                  this.isEdit = false;
                  this.getCompanyDetail()
                }
              });
            }
          } else {
            this.$message.error('请检查表单内容');
          }
        },
        handleClose() {
            this.$confirm('是否确认放弃修改？', '提示', {
            confirmButtonText: '确认',
            type: 'warning'
            }).then(async () => {
                this.isEdit = false;
            }).catch(() => {
            });
        },
    },
  };
  </script>
  
  <style scoped>
  .enterprise-detail {
    padding: 20px 20px 30px;
    border-radius: 8px;
    background-color: #FFFFFF;
    margin-top: 20px;
  }
  .enterprise-detail-title {
    color: #17233D;
    font-weight: 500;
    font-size: 24px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .enterprise-detail-tabs{
    padding: 10px 20px;
    border: 1px solid #EDEDED;
  }
  </style>