@import './variables.scss';

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  font-family: $font-family-base;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  color: #222222;
  background-color: $bg-body;
}

ul,
ol,
li {
  list-style: none;
}

a {
  text-decoration: none;
}
.table_Header {
  th {
    background-color: #fafbfc !important;
    padding: 0 !important;
    height: 50px;
    line-height: 50px;
    font-size: 14px !important;
    text-align: center;

    div {
      line-height: 50px !important;
      word-break: keep-all !important;
      word-wrap: break-word !important;
    }

    .cell {
      font-weight: bold !important;
    }
  }
}
.demo-form-inline{
  .el-form-item{
    margin-bottom: 0;
    margin-right: 20px !important;
  }
}
.pagination-wrapper {
  width: 100%;
  padding: 8px 10px 8px 10px;
  border: 1px solid #EDEDED;
  border-top: none;
  box-sizing: border-box;
}
.el-dialog__header {
  background-color: #EFF1F4;
  padding: 14px 20px 14px;
}
.el-dialog__footer {
  text-align: center !important;
}

