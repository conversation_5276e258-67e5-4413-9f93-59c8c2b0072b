<template>
  <div class="navbar">
    <div class="left-menu">
      <!-- Logo和系统名称 -->
      <div class="logo-container">
        <img src="@/assets/britc-logo.png" alt="Logo" class="logo" v-if="false" />
        <span class="title">舟山离岸贸易信息服务平台</span>
      </div>
      <!-- <i class="el-icon-s-fold" @click="toggleSideBar"></i> -->
    </div>

    <div class="right-menu">
      <!-- 用户信息 -->
      <el-dropdown trigger="click" class="avatar-container">
        <div class="avatar-wrapper">
          <img style="width: 24px;height: 24px;" src="@/assets/images/avatar.png" class="user-avatar" />
          <span class="user-name">管理员</span>
          <i class="el-icon-caret-bottom"></i>
        </div>
        <el-dropdown-menu slot="dropdown" class="user-dropdown">
          <el-dropdown-item>
            <img src="@/assets/images/user.png" alt="" class="item-avatar" />
            <span @click="handleProfile">航贸通信息</span>
          </el-dropdown-item>
          <el-dropdown-item>
            <img src="@/assets/images/password.png" alt="" class="item-avatar" />
            <span @click="handlePassWord">修改密码</span>
          </el-dropdown-item>
          <el-dropdown-item>
            <img src="@/assets/images/logout.png" alt="" class="item-avatar" />
            <span @click="logout">退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <el-dialog title="修改密码" :visible.sync="dialogVisible" width="40%">
      <el-form v-if="!changeSuccess" ref="passwordForm" label-position="top" :model="passwordForm" :rules="rules" label-width="100px">
        <el-form-item label="当前密码" prop="currentPassword">
          <el-input v-model="passwordForm.currentPassword" type="password" placeholder="请输入当前密码"></el-input>
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="passwordForm.newPassword" type="password" placeholder="请输入新密码"></el-input>
        </el-form-item>
        <el-form-item label="确认新密码" prop="confirmPassword">
          <el-input v-model="passwordForm.confirmPassword" type="password" placeholder="请再次输入新密码"></el-input>
        </el-form-item>
      </el-form>
      <div v-if="changeSuccess" class="success-message">
        <img src="@/assets/images/Icon_success.png" alt="Icon_success" style="width: 60px;">
        <div class="success-message-text">密码重置成功!</div>
        <p class="success-message-tips">您的密码已经重置完成</p>
        <div class="success-message-tips2">
          <p> ·  请使用新密码重新登录系统</p>
          <p> · 为了财产安全，请妥善保管您的密码</p>
          <p> ·  如遇问题，请联系系统管理员</p>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button v-if="!changeSuccess" @click="dialogVisible = false">取消</el-button>
        <el-button v-if="!changeSuccess" type="primary" @click="handleConfirm">确认修改</el-button>
        <el-button v-if="changeSuccess" type="primary" @click="nextStep">完成</el-button>
      </span>
    </el-dialog>
    <el-dialog class="qrCodeDialog" title="授权DID绑定" :visible.sync="DIDDialogVisible" width="40%">
      <div v-if="!showDID" @click="getQRCode()" class="qr-code-wrapper">
        <QRCodes alt="" :url='qrCodeUrl'></QRCodes>
        <div v-if="isExpired" class="expired-overlay">
          <img src="@/assets/images/Refresh.png" alt="Refresh" style="width: 20px;">
          <p>二维码已失效<br />请点击刷新</p>
        </div>
      </div>
      <div v-else>
        <div class="did-wrapper">
          <span style="margin-right: 10px;">DID地址:</span><sapn>{{ this.showDIDInfo.did }}</sapn>
        </div>
        <div class="did-wrapper">
          <span style="margin-right: 10px;">DID颁发机构:</span><sapn>{{ this.showDIDInfo.didIssuer }}</sapn>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import { mapGetters } from 'vuex'
import { resetPassword } from '@/apis/auth'
import { getDIDCodeApi,generateDidQrCode } from '@/apis/index'
import { encrypt } from '@/utils/slylLoginRsaEncrypt'
import QRCodes from "@/component/comQRCode.vue"

export default {
  name: 'Navbar',
  components: {
    QRCodes,
  },
  data() {
    const validateNewPassword = (rule, value, callback) => {
      const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
      if (!regex.test(value)) {
        callback(new Error('密码为至少8位且区分大小字母、数字和特殊字符的组合，如AAbb11&22'));
      } else {
        callback();
      }
    };
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入密码不一致'));
      } else {
        callback();
      }
    };
    return {
      dialogVisible: false,
      changeSuccess: false,
      isExpired: false,
      showDID:false,
      showDIDInfo:{},
      DIDDialogVisible: false,
      requestId:'',
      intervalId: null,
      timeoutId: null,
      qrCodeUrl: "",
      passwordForm: {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      rules: {
        currentPassword: [
          { required: true, message: '请输入当前密码', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { validator: validateNewPassword, trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请再次输入新密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapGetters(['getUser']),
    user() {
      return this.getUser
    }
  },
  beforeDestroy() {
    if (this.countdownInterval) {
      clearInterval(this.countdownInterval);
    }
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  },
  methods: {
    handleProfile() {
      this.DIDDialogVisible = true
      this.getQRCode()
    },
    startExpirationCheck() {
      setInterval(() => {
        if (Date.now() >= this.expirationTime) {
          this.isExpired = true;
        }
      }, 1000);
    },
    getQRCode() {
      clearInterval(this.intervalId)
      getDIDCodeApi().then(res => {
        if (res.code == 200) {
          if (res.data.bound) {
            this.showDIDInfo = res.data
            this.showDID = true
          } else {
            this.requestId = res.data.queryId
            this.qrCodeUrl = res.data.qrCodeUrl
            this.getDidInfo()
            this.expirationTime = Date.now() + 1 * 60 * 1000; // 5分钟后过期
            this.startExpirationCheck();
            this.timeoutId = setTimeout(() => {
              console.log(this.intervalId,'intervalId');
              clearInterval(this.intervalId)
              clearTimeout(this.timeoutId);
              this.intervalId = null;
            }, 60000);
          }
        }
      })
      this.isExpired = false;
    },
    //扫码获取did信息
    getDidInfo(){
      const data = {
        queryId: this.requestId,
      };
      this.intervalId = setInterval(async () =>{
        try {
          const res = await generateDidQrCode(data); // 使用 await 等待 action 返回结果
          if (res.data) {
            clearInterval(this.intervalId); // 成功停止轮询
            this.$message.success('DID绑定成功')
            this.showDIDInfo = res.data
            this.showDID = true
            // this.DIDDialogVisible = false
          }
        } catch (error) {
          console.error('扫码登录失败:', error);
        }
      }, 5000);
    },
    async logout() {
      try {
        // 清除token
        Cookies.remove('AMS-SIMPLE-ADMIN-TRADE-TOKEN')
        this.$store.dispatch('auth/logout')
        // 跳转到登录页
        this.$router.push('/login')
      } catch (error) {
        console.error('退出登录失败:', error)
      }
    },
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    handlePassWord() {
      this.dialogVisible = true
    },
    async updatePassword() {
      // 模拟密码更新接口
      return new Promise((resolve) => setTimeout(resolve, 1000));
    },
    handleConfirm() {
      this.$refs.passwordForm.validate(async (valid) => {
        if (valid) {
          try {
            console.log(this.user,'this.user');
            
            const data = {
              oldPassword: encrypt(this.passwordForm.currentPassword),
              newPassword: encrypt(this.passwordForm.newPassword),
              confirmPassword: encrypt(this.passwordForm.confirmPassword),
              userId: this.user.userId,
              type: '1'
            }
            resetPassword(data).then(res => {
              console.log(res, '-------');
              if (res.code === 200) {
                this.$message.success('密码修改成功');
                this.changeSuccess = false
                this.dialogVisible = false;
              }
            })
          } catch (error) {
            this.$message.error('密码修改失败，请重试');
          }
        } else {
          // this.$message.warning('请正确填写密码信息');
        }
      });
    },
    nextStep() {
      this.dialogVisible = false
      this.changeSuccess = false
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 60px;
  overflow: hidden;
  z-index: 1002;
  background-color: #3566F4;
  color: #FFFFFF;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 15px;
  color: #fff;
}

.left-menu {
  display: flex;
  align-items: center;
}

.hamburger-container {
  line-height: 46px;
  height: 100%;
  float: left;
  cursor: pointer;
  transition: background 0.3s;
  padding: 0 15px;

  &:hover {
    background: rgba(0, 0, 0, 0.025);
  }

  i {
    font-size: 20px;

    &.is-active {
      transform: rotate(180deg);
    }
  }
}

.logo-container {
  display: flex;
  align-items: center;
  margin-left: 10px;

  .logo {
    width: 32px;
    height: 32px;
    margin-right: 10px;
  }

  .title {
    font-size: 20px;
    font-weight: bold;
    color: #FFFFFF;
  }
}

.right-menu {
  display: flex;
  align-items: center;
}
.success-message{
 text-align: center;
}
.success-message-text{
  color: #027A48;
  font-weight: 700;
  font-size: 24px;
  margin: 20px 0 10px;
}
.success-message-tips{
  color: #515A6E;
  font-weight: 400;
  font-size: 16px;
  margin-bottom: 30px;
}
.success-message-tips2{
  color: #808695;
  font-weight: 400;
  font-size: 14px;
}
/deep/ .el-dropdown-menu__item{
  display: flex;
  align-items: center;
}
.item-avatar{
  width: 16px;
  height: 16px;
  margin-right: 5px;
}
.qr-code-wrapper {
  position: relative;
  width: 200px;
  height: 200px;
  .qr-code-img {
    width: 100%;
    height: 100%;
  }
  .expired-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); // 透明遮罩
    color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    p {
      font-size: 14px;
      line-height: 1.5;
    }
  }
}
.expired-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5); // 透明遮罩
  color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  p {
    font-size: 14px;
    line-height: 1.5;
  }
}
.expired {
  .qr-code-img {
    opacity: 0.5; // 过期时二维码半透明
  }
}
.did-wrapper{
  margin-bottom: 20px;
}

.avatar-container {
  margin-right: 30px;

  .avatar-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;
    border-radius: 21px;
    background-color: rgba(255, 255, 255, 0.08);
    padding: 10px;

    .user-avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      margin-right: 8px;
    }
    /deep/ .el-icon-caret-bottom:before{
      color: #FFFFFF !important;
    }

    .user-name {
      font-size: 14px;
      color: #FFFFFF;
      margin-right: 5px;
    }
  }
}
/deep/.el-dialog__body {
  // text-align: -webkit-center;
  display: flex;
    justify-content: center;
}
</style>

