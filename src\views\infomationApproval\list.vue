<template>
  <div>
    <h3>申报信息批复</h3>
    <div class="trade-list-container">
      <!-- 搜索表单 -->

      <el-form :inline="true" :model="searchForm" label-position="top" class="search-form">
        <el-form-item label="业务编号">
          <el-input v-model="searchForm.reportNo" placeholder="请输入业务编号" clearable></el-input>
        </el-form-item>
        <el-form-item label="企业名称">
          <el-autocomplete
            v-model="searchForm.businessName"
            style="width: 100%"
            :fetch-suggestions="querySearchAsync"
            value-key="companyName"
            value="id"
            :debounce="500"
            placeholder="请输入企业名称"
            @select="handleSelectCompany"
          ></el-autocomplete>
        </el-form-item>
        <el-form-item label="申报日期">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="~"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <div class="add-btn">
        <span
          class="tags pending-verification"
          :class="{ active: searchForm.type === 'instruction_pending' }"
          @click="changeTab('instruction_pending')"
        >
          待批复
          <span class="tags-badge">{{ instructionPendingData?.length || 0 }}</span>
        </span>
        <span
          class="tags"
          :class="{ active: searchForm.type === 'instruction_finished' }"
          @click="changeTab('instruction_finished')"
        >
          已批复
        </span>
        <span
          v-if="authType === '105'"
          class="tags pending-verification"
          :class="{ active: searchForm.type === 'reply_submit' }"
          @click="changeTab('reply_submit')"
        >
          待复议
          <span class="tags-badge">{{ replySubmitData?.length || 0 }}</span>
        </span>
        <span
          v-if="authType === '105'"
          class="tags"
          :class="{ active: searchForm.type === 'reply_finished' }"
          @click="changeTab('reply_finished')"
        >
          已复议
        </span>
      </div>
      <el-table key="stamp-table" :data="tableData" class="table_Header" border style="width: 100%">
        <el-table-column type="index" width="50" label="序号"> </el-table-column>
        <el-table-column prop="reportNo" label="业务编号" width="120"></el-table-column>
        <el-table-column prop="contactNos" label="合同编号" width="180"></el-table-column>
        <el-table-column prop="eblNos" label="提单号" width="180"></el-table-column>
        <el-table-column prop="declareDateStr" label="申报日期" width="180"></el-table-column>
        <el-table-column prop="businessInfo.companyName" label="企业名称"></el-table-column>
        <el-table-column prop="businessInfo.creditCode" label="企业组织机构代码"></el-table-column>
        <el-table-column label="操作" width="120">
          <template slot-scope="scope">
            <img
              v-if="searchForm.type === 'instruction_pending' || searchForm.type === 'reply_submit'"
              class="action-img"
              src="@/assets/images/review.png"
              alt=""
              @click="handleEdit(scope.row, 'edit')"
            />
            <img
              v-if="
                searchForm.type === 'instruction_finished' || searchForm.type === 'reply_finished'
              "
              class="action-img"
              src="@/assets/images/view.png"
              alt=""
              @click="handleEdit(scope.row, 'view')"
            />
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { getTradeListDataApi, getCompanyListDataApi } from '@/apis/index'
import { mapGetters } from 'vuex'

export default {
  name: 'InfomationApprovalList',
  data() {
    return {
      searchForm: {
        reportNo: '',
        businessId: '',
        businessName: '',
        dateRange: '',
        type: 'instruction_pending'
      },
      loading: false,
      instructionPendingData: [],
      instructionFinishedData: [],
      replySubmitData: [],
      replyFinishedData: [],
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
    }
  },
  computed: {
    ...mapGetters(['authType']),
    tableData() {
      switch (this.searchForm.type) {
        case 'instruction_pending':
          return this.instructionPendingData
        case 'instruction_finished':
          return this.instructionFinishedData
        case 'reply_submit':
          console.log(111)
          return this.replySubmitData
        case 'reply_finished':
          return this.replyFinishedData
        default:
          return []
      }
    }
  },
  watch: {
    'searchForm.businessName': function (val) {
      if (!val) {
        this.searchForm.businessId = ''
      }
    }
  },

  async mounted() {
    await this.handleSearch('instruction_pending')
    if (this.authType === '105') {
      await this.handleSearch('reply_submit')
    }
  },
  methods: {
    changeTab(str) {
      this.searchForm.type = str
      this.pagination.total = 0
      this.handleSearch(str)
    },
    async handleSearch(type) {
      const param = {
        pageNum: this.pagination.pageNum,
        pageSize: this.pagination.pageSize,
        reportNo: this.searchForm.reportNo,
        status: type || this.searchForm.type,
        businessId: this.searchForm.businessId,
        startDate: this.searchForm.dateRange?.[0] || '',
        endDate: this.searchForm.dateRange?.[1] || ''
      }
      return getTradeListDataApi(param).then((res) => {
        if (res.code === 200) {
          if (type === 'instruction_pending') {
            this.instructionPendingData = res.data.records
          } else if (type === 'instruction_finished') {
            this.instructionFinishedData = res.data.records
          } else if (type === 'reply_submit') {
            this.replySubmitData = res.data.records
          } else if (type === 'reply_finished') {
            this.replyFinishedData = res.data.records
          }
          this.pagination.total = res.data.total
        }
      })
    },
    querySearchAsync(queryString, cb) {
      getCompanyListDataApi({ companyName: queryString }).then((res) => {
        cb(res.data)
      })
    },
    handleSelectCompany(value) {
      if (value?.id) {
        this.searchForm.businessId = value.id
      } else {
        this.searchForm.businessId = ''
      }
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.handleSearch()
    },
    handleCurrentChange(val) {
      this.pagination.currentPage = val
      this.handleSearch()
    },
    handleEdit(row, type) {
      // 跳转到编辑页
      this.$router.push({
        name: 'InfomationApprovalDetail',
        params: { id: row.id },
        query: { mode: type }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.trade-list-container {
  padding: 20px;
  background-color: #fff;
  margin-top: 20px;
}
.search-form {
  margin-bottom: 20px;
  display: flex;
  align-items: flex-end;
  .el-form-item {
    margin-right: 20px;
  }
}
.add-btn {
  display: flex;
  align-items: center;
  background-color: #fafbfc;
  border: 1px solid #ededed;
  border-bottom: none;
  padding-left: 20px;
  height: 48px;

  .tags {
    padding-bottom: 10px;
    margin: 0 10px;
    position: relative;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    .tags-badge {
      position: absolute;
      top: 0;
      right: -30px;
      border-radius: 50%;
      background-color: red;
      color: #fff;
      width: 20px;
      height: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    &.active {
      color: #3566f4;
      border-bottom: 2px solid #3566f4;
    }
  }
  .pending-verification {
    margin-right: 40px;
  }
}
.verification-btn {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 48px;
}
.status-label {
  display: flex;
  align-items: center;
  &::before {
    content: '';
    display: inline-block;
    box-sizing: border-box;
    width: 10px;
    height: 10px;
    margin-right: 10px;
    border-radius: 50%;
    border: 2px solid rgba($color: #fff, $alpha: 0.8);
  }
}
.draft {
  color: #3566f4;
  &::before {
    background-color: #3566f4;
  }
}
.submitted {
  color: #027a48;
  &::before {
    background-color: #027a48;
  }
}
.canceled {
  color: #515a6e;
  &::before {
    background-color: #515a6e;
  }
}
.info-img {
  width: 16px;
  height: 16px;
  margin-left: 15px;
}
.action-img {
  width: 24px;
  height: 24px;
  margin-right: 10px;
}
.pagination-container {
  margin-top: 20px;
}
</style>
