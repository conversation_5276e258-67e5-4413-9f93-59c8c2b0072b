/**
 * 校验手机号
 * @param number
 * @returns {{msg: string, success: boolean}}
 */
export function verifyNumber(rule, number, callback) {
    if (!number) {
      callback();
    } else {
      const reg = /^1([38][0-9]|4[579]|5[0-3,5-9]|6[6]|7[0135678]|9[89])\d{8}$/;
      if (number.length !== 11) {
        callback(new Error('请填入11位手机号'));
      }
      if (!reg.test(number)) {
        callback(new Error('请填入正确的手机号'));
      } else {
        callback();
      }
    }
}
  
/**
 * 校验手机号
 * @param number
 * @returns {{msg: string, success: boolean}}
 */
export function verifyCompanyNumber(rule, number, callback) {
    if (!number) {
      callback();
    } else {
      const reg = /^1([38][0-9]|4[579]|5[0-3,5-9]|6[6]|7[0135678]|9[89])\d{8}$/;
      const landlineReg = /^\d{3,4}-\d{7,8}$/;
      if (!reg.test(number) && !landlineReg.test(number)) {
        callback(new Error('请填入正确的手机号（支持11位手机号或固定电话，如0571-1234567）'));
      } else {
        callback();
      }
    }
}

export function validateLandline (rule, value, callback) {
  const pattern = /^(\d{3,4})-(\d{7,8})$/;
  if (!pattern.test(value)) {
    callback(new Error('请输入正确的固定电话格式，例如：0571-1234567'));
  } else {
    callback();
  }
};
  
/**
 * 校验邮箱
 * @param email
 */
export function verifyEmail(rule, email, callback) {
    if (!email) {
      callback();
    } else {
        let reg = /^[a-zA-Z0-9]+([-_.][A-Za-zd]+)*@([a-zA-Z0-9]+[-.])+[A-Za-zd]{2,5}$/;
      if (!reg.test(email)) {
        callback(new Error('请输入正确的邮箱地址'));
      } else {
        callback();
      }
    }
}
export function validateBankEmail (rule, value, callback) {
  // 标准邮箱正则
  const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  
  // 银行域名标识白名单（可扩展）
  const bankDomains = ['.bank', '.中国银行', 'bank.', 'chinabank', 'icbc', 'ccb', 'abc'];

  if (!emailPattern.test(value)) {
    callback(new Error('请输入正确的邮箱格式'));
  } else {
    const domain = value.split('@')[1];
    const isBankDomain = bankDomains.some(bank => domain.includes(bank));
    
    if (!isBankDomain) {
      callback(new Error('邮箱域名需包含银行标识，如 .bank、bank.com 等'));
    } else {
      callback();
    }
  }
};
  
export function validateNewPassword (rule, value, callback){
  const lengthCheck = /.{8,}/;
  const hasLower = /(?=.*[a-z])/;
  const hasUpper = /(?=.*[A-Z])/;
  const hasDigit = /(?=.*\d)/;
  const hasSpecial = /[\\'.,:;*?~`!@#$%^&+=()<>{}[\]/"|$]/;

  if (!lengthCheck.test(value)) {
    callback(new Error('密码长度至少为8位'));
  } else if (!hasLower.test(value)) {
    callback(new Error('必须包含小写字母'));
  } else if (!hasUpper.test(value)) {
    callback(new Error('必须包含大写字母'));
  } else if (!hasDigit.test(value)) {
    callback(new Error('必须包含数字'));
  } else if (!hasSpecial.test(value)) {
    callback(new Error('必须包含特殊字符，如 ! @ # $ % ^ & * 等'));
  } else {
    callback();
  }
};