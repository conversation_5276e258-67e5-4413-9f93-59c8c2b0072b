<template>
  <div>
    <h3>核验报告</h3>
    <div class="trade-list-container">
      <!-- 搜索表单 -->

      <el-form :inline="true" :model="searchForm" label-position="top" class="search-form">
        <el-form-item label="业务编号">
          <el-input
            v-model="searchForm.contactNos"
            placeholder="请输入业务编号"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="企业名称">
          <el-input
            v-model="searchForm.contactNos"
            placeholder="请输入企业名称"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="报告日期">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="~"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table key="stamp-table" :data="tableData" class="table_Header" border style="width: 100%">
        <el-table-column type="index" width="50" label="序号"> </el-table-column>
        <el-table-column prop="reportNo" label="业务编号" width="120"></el-table-column>
        <el-table-column prop="contactNos" label="合同编号" width="260"></el-table-column>
        <el-table-column prop="eblNos" label="提单号" width="180"></el-table-column>
        <el-table-column prop="declareDateStr" label="企业名称" width="180"></el-table-column>
        <el-table-column
          prop="declareDateStr"
          label="企业组织机构代码"
          width="180"
        ></el-table-column>

        <el-table-column
          prop="bankInfo.companyName"
          label="金融机构名称"
          width="180"
        ></el-table-column>
        <el-table-column
          prop="bankInfo.financialInstitutionCode"
          label="金融机构代码"
          width="180"
        ></el-table-column>
        <el-table-column prop="declareDateStr" label="报告日期" width="180"></el-table-column>

        <el-table-column prop="businessStatus" label="业务状态" width="140">
          <template slot-scope="scope">
            <span class="status-label">
              <el-tooltip
                v-if="scope.row.businessStatus === 'cancel'"
                class="item"
                effect="dark"
                content="业务未通过审核，请重新申报"
                placement="top"
              >
                <img src="@/assets/images/danger-info.png" class="info-img" alt="" />
              </el-tooltip>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <img
              class="action-img"
              src="@/assets/images/view.png"
              alt=""
              @click="handleEdit(scope.row, 'view')"
            />
            <img
              v-if="scope.row.replyStatus === 'no_reply' && scope.row.isFinished === '0'"
              class="action-img"
              src="@/assets/images/blue-apply.png"
              alt=""
              @click="handleEdit(scope.row, 'edit')"
            />
            <img v-else class="action-img" src="@/assets/images/white-apply.png" alt="" />
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="pagination.pageNum"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getReportListApi } from '@/apis/index'
export default {
  name: 'StampDeclaration',
  data() {
    return {
      searchForm: {
        contactNos: '',
        status: '',
        eblNo: '',
        dateRange: ''
      },
      loading: false,
      tableData: [],
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
    }
  },
  computed: {
    ...mapGetters(['tradeStatusParam'])
  },
  mounted() {
    this.handleSearch()
    this.$dispatch('enum/getTradeStatusParam')
  },
  methods: {
    handleSearch() {
      const param = {
        pageNum: this.pagination.pageNum,
        pageSize: this.pagination.pageSize,
        contactNos: this.searchForm.contactNos,
        status: this.searchForm.status,
        eblNo: this.searchForm.eblNo,
        startDate: this.searchForm.dateRange?.[0] || '',
        endDate: this.searchForm.dateRange?.[1] || ''
      }
      getReportListApi(param).then((res) => {
        if (res.code === 200) {
          this.tableData = res.data.records
          this.pagination.total = res.data.total
        }
      })
    },
    resetSearch() {
      this.searchForm = {
        contactNos: '',
        status: '',
        eblNo: '',
        dateRange: ''
      }
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.handleSearch()
    },
    handleCurrentChange(val) {
      this.pagination.pageNum = val
      this.handleSearch()
    },
    handleEdit(row, type) {
      // 跳转到编辑页
      this.$router.push({
        name: 'ReconsiderationDeclarationDetail',
        params: { id: row.id },
        query: { mode: type }
      })
    },
    handleRefreshSubmit() {}
  }
}
</script>

<style lang="scss" scoped>
.trade-list-container {
  padding: 20px;
  background-color: #fff;
  margin-top: 20px;
}
.search-form {
  margin-bottom: 20px;
  display: flex;
  align-items: flex-end;
  .el-form-item {
    margin-right: 20px;
  }
}
.add-btn {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  background-color: #fafbfc;
  border: 1px solid #ededed;
  border-bottom: none;
  padding-right: 20px;
  height: 48px;
}
.status-label {
  display: flex;
  align-items: center;
  &::before {
    content: '';
    display: inline-block;
    box-sizing: border-box;
    width: 10px;
    height: 10px;
    margin-right: 10px;
    border-radius: 50%;
    border: 2px solid rgba($color: #fff, $alpha: 0.8);
  }
}
.draft {
  color: #3566f4;
  &::before {
    background-color: #3566f4;
  }
}
.submitted {
  color: #027a48;
  &::before {
    background-color: #027a48;
  }
}
.cancel {
  color: #515a6e;
  &::before {
    background-color: #515a6e;
  }
}
.info-img {
  width: 16px;
  height: 16px;
  margin-left: 15px;
}
.action-img {
  width: 24px;
  height: 24px;
  margin-right: 10px;
}
.pagination-container {
  margin-top: 20px;
}
</style>

