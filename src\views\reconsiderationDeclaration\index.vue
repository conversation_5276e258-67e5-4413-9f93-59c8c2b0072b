<template>
  <div>
    <h3>核验报告</h3>
    <div class="trade-list-container">
      <!-- 搜索表单 -->

      <el-form :inline="true" :model="searchForm" label-position="top" class="search-form">
        <el-form-item label="业务编号">
          <el-input v-model="searchForm.reportNo" placeholder="请输入业务编号" clearable></el-input>
        </el-form-item>
        <el-form-item label="企业名称">
          <el-autocomplete
            v-model="searchForm.businessName"
            style="width: 100%"
            :fetch-suggestions="querySearchAsync"
            value-key="companyName"
            value="id"
            :debounce="500"
            placeholder="请输入企业名称"
            @select="handleSelectCompany"
          ></el-autocomplete>
        </el-form-item>
        <el-form-item label="报告日期">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="~"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table key="stamp-table" :data="tableData" class="table_Header" border style="width: 100%">
        <el-table-column type="index" width="50" label="序号"> </el-table-column>
        <el-table-column prop="reportNo" label="业务编号" width="120"></el-table-column>
        <el-table-column prop="contactNos" label="合同编号" width="260"></el-table-column>
        <el-table-column prop="eblNos" label="提单号" width="180"></el-table-column>
        <el-table-column
          prop="businessInfo.companyName"
          label="企业名称"
          width="180"
        ></el-table-column>
        <el-table-column
          prop="businessInfo.creditCode"
          label="企业组织机构代码"
          width="180"
        ></el-table-column>

        <el-table-column
          prop="bankInfo.companyName"
          label="金融机构名称"
          width="180"
        ></el-table-column>
        <el-table-column
          prop="bankInfo.financialInstitutionCode"
          label="金融机构代码"
          width="180"
        ></el-table-column>
        <el-table-column prop="declareDateStr" label="报告日期" width="180"></el-table-column>

        <el-table-column prop="businessStatus" label="业务状态" width="140">
          <template slot-scope="scope">
            <div class="status-container">
              <span class="status-text" :class="getStatusLabel(scope.row).type">{{
                getStatusLabel(scope.row).label || ''
              }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <img
              class="action-img"
              src="@/assets/images/view.png"
              alt=""
              @click="handleEdit(scope.row, 'view')"
            />
            <template v-if="authType === '103'">
              <el-tooltip
                v-if="scope.row.replyStatus === 'no_reply' && scope.row.isFinished === '0'"
                class="item"
                effect="dark"
                content="复议申请"
                placement="top"
              >
                <img
                  class="action-img"
                  src="@/assets/images/blue-apply.png"
                  alt=""
                  @click="handleEdit(scope.row, 'edit')"
                />
              </el-tooltip>
              <el-tooltip
                v-else
                class="item"
                effect="dark"
                content="已发起复议申请"
                placement="top"
              >
                <img class="action-img" src="@/assets/images/white-apply.png" alt="" />
              </el-tooltip>
              <el-tooltip
                v-if="scope.row.isFinished === '0'"
                class="item"
                effect="dark"
                content="完结"
                placement="top"
              >
                <img
                  class="action-img"
                  src="@/assets/images/apply-finished.png"
                  alt=""
                  @click="finishApply(scope.row)"
                />
              </el-tooltip>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="pagination.pageNum"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>
    <el-dialog title="复议申请" :visible.sync="dialogFormVisible" width="40%">
      <el-form :model="form" label-position="top">
        <el-form-item label="申请复议说明">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入复议说明"
            rows="5"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button :disabled="!form.remark" type="primary" @click="submitHandler">
          确 定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getReportListApi, reviewTradeSubmitApi, getCompanyListDataApi } from '@/apis/index'
export default {
  name: 'ReconsiderationDeclarationList',
  data() {
    return {
      searchForm: {
        businessId: '',
        businessName: '',
        reportNo: '',
        dateRange: ''
      },
      loading: false,
      tableData: [],
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      form: {
        remark: ''
      },
      dialogFormVisible: false,
      id: ''
    }
  },
  computed: {
    ...mapGetters(['tradeStatusParam', 'authType'])
  },
  watch: {
    'searchForm.businessName': function (val) {
      if (!val) {
        this.searchForm.businessId = ''
      }
    }
  },
  mounted() {
    this.handleSearch()
    this.$dispatch('enum/getTradeStatusParam')
  },
  methods: {
    querySearchAsync(queryString, cb) {
      getCompanyListDataApi({ companyName: queryString }).then((res) => {
        cb(res.data)
      })
    },
    handleSelectCompany(value) {
      if (value?.id) {
        this.searchForm.businessId = value.id
      } else {
        this.searchForm.businessId = ''
      }
    },
    handleSearch() {
      const param = {
        pageNum: this.pagination.pageNum,
        pageSize: this.pagination.pageSize,
        businessId: this.searchForm.businessId,
        reportNo: this.searchForm.reportNo,
        startDate: this.searchForm.dateRange?.[0] || '',
        endDate: this.searchForm.dateRange?.[1] || ''
      }
      getReportListApi(param).then((res) => {
        if (res.code === 200) {
          this.tableData = res.data.records
          this.pagination.total = res.data.total
        }
      })
    },
    resetSearch() {
      this.searchForm = {
        contactNos: '',
        status: '',
        eblNo: '',
        dateRange: ''
      }
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.handleSearch()
    },
    handleCurrentChange(val) {
      this.pagination.pageNum = val
      this.handleSearch()
    },
    handleEdit(row, type) {
      // 跳转到编辑页
      if (type === 'view') {
        this.$router.push({
          name: 'ReconsiderationDeclarationDetail',
          params: { id: row.id }
        })
      } else {
        this.id = row.id
        this.dialogFormVisible = true
      }
    },
    getStatusLabel(row) {
      // 根据业务状态返回对应的文字标签
      if (row.replyStatus === 'no_reply') {
        return {
          label: '正常',
          type: 'normal'
        }
      } else if (row.isFinished === '0') {
        return {
          label: '复议中',
          type: 'reply-submit'
        }
      } else if (row.isFinished === '1') {
        return {
          label: '已结束',
          type: 'finished'
        }
      } else {
        return {}
      }
    },
    finishApply(row) {
      this.$confirm('是否确认完结，完结后，将通过电子邮件的方式通知相关方', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const param = {
            id: Number(row.id),
            action: 'finish'
          }
          reviewTradeSubmitApi(param).then((res) => {
            if (res.code === 200) {
              this.$message({
                message: '完结成功',
                type: 'success'
              })
              this.handleSearch()
            }
          })
        })
        .catch(() => {})
    },
    submitHandler() {
      const param = {
        id: Number(this.id),
        remark: this.form.remark,
        action: 'apply'
      }
      reviewTradeSubmitApi(param).then((res) => {
        if (res.code === 200) {
          this.$message({
            message: '复议申请成功',
            type: 'success'
          })
          this.dialogFormVisible = false
          this.handleSearch()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.trade-list-container {
  padding: 20px;
  background-color: #fff;
  margin-top: 20px;
}
.search-form {
  margin-bottom: 20px;
  display: flex;
  align-items: flex-end;
  .el-form-item {
    margin-right: 20px;
  }
}
.add-btn {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  background-color: #fafbfc;
  border: 1px solid #ededed;
  border-bottom: none;
  padding-right: 20px;
  height: 48px;
}
.status-label {
  display: flex;
  align-items: center;
  &::before {
    content: '';
    display: inline-block;
    box-sizing: border-box;
    width: 10px;
    height: 10px;
    margin-right: 10px;
    border-radius: 50%;
    border: 2px solid rgba($color: #fff, $alpha: 0.8);
  }
}
.draft {
  color: #3566f4;
  &::before {
    background-color: #3566f4;
  }
}
.submitted {
  color: #027a48;
  &::before {
    background-color: #027a48;
  }
}
.cancel {
  color: #515a6e;
  &::before {
    background-color: #515a6e;
  }
}
.info-img {
  width: 16px;
  height: 16px;
  margin-left: 15px;
}
.action-img {
  width: 24px;
  height: 24px;
  margin-right: 10px;
}
.pagination-container {
  margin-top: 20px;
}
.status-text {
  font-size: 14px;
  display: flex;
  align-items: center;
  color: #515a6e;
  &::before {
    content: '';
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #515a6e;
    margin-right: 10px;
    border: 2px solid rgba(255, 255, 255, 0.8);
  }
  &.reply-submit {
    color: #b54708;
    &::before {
      background-color: #b54708;
    }
  }
  &.normal {
    color: #3566f4;
    &::before {
      background-color: #3566f4;
    }
  }
}
</style>

