<template>
  <div class="sidebar-container">
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        :active-text-color="variables.menuActiveText"
        :unique-opened="false"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item
          v-for="route in routes"
          :key="route.path"
          :item="route"
          :base-path="route.path"
          :is-collapse="isCollapse"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import SidebarItem from './SidebarItem'
import { mapGetters } from 'vuex'
import { getAccessibleMenuRoutes } from '@/utils/permission'

export default {
  name: 'Sidebar',
  components: { SidebarItem },
  computed: {
    ...mapGetters(['sidebar', 'userInfo']),
    routes() {
      // 从路由配置中获取所有路由
      const allRoutes = this.$router.options.routes

      // 根据用户权限过滤可访问的菜单路由
      return getAccessibleMenuRoutes(allRoutes, this.userInfo.user, this.userInfo.authType)
    },
    isCollapse() {
      return !this.sidebar.opened
    },
    activeMenu() {
      const { meta, path } = this.$route
      // 如果设置了高亮路径，则使用设置的路径
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    variables() {
      return {
        menuBg: '#304156',
        menuText: '#bfcbd9',
        menuActiveText: '#409EFF'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.sidebar-container {
  height: 100%;
  background-color: #304156;
  transition: all 0.3s ease-in-out;

  .el-scrollbar {
    height: 100%;
  }
  .scrollbar-wrapper {
    overflow-x: hidden !important;
  }

  .el-scrollbar__view {
    height: 100%;
  }

  .el-menu {
    border: none;
    height: 100%;
    width: 100% !important;
    background-color: #fff !important;
  }

  // 折叠菜单时的样式
  .el-menu--collapse {
    .el-submenu__title {
      span {
        display: none;
      }

      .el-submenu__icon-arrow {
        display: none;
      }
    }
  }

  // 图标样式
  i[class^='el-icon-'] {
    margin-right: 10px;
    font-size: 18px;
    vertical-align: middle;
  }
}
</style>

