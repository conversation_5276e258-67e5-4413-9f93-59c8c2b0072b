<template>
  <div class="sidebar-container">
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        :active-text-color="variables.menuActiveText"
        :unique-opened="false"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item
          v-for="route in routes"
          :key="route.path"
          :item="route"
          :base-path="route.path"
          :isCollapse="isCollapse"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import SidebarItem from './SidebarItem'
import { mapGetters } from 'vuex'

export default {
  name: 'Sidebar',
  components: { SidebarItem },
  data() {
    return {
      routes: [
        {
          path: '/enterpriseInfoMaintenance',
          name: 'enterpriseInfoMaintenance',
          meta: { title: '企业信息维护' }
        },
        {
          path: '/bankMaintenance',
          name: 'bankMaintenance',
          meta: { title: '商业银行维护' }
        },
        {
          path: '/stamp-declaration/list',
          meta: {
            title: '印花税减免申报',
            requiresAuth: true
          }
        },
        {
          path: '/trade-verification/list',
          meta: {
            title: '离岸贸易业务核验',
            requiresAuth: true
          }
        },
        {
          path: '/infomation-review/list',
          meta: {
            title: '申报信息审核',
            requiresAuth: true
          }
        },
        {
          path: '/infomation-approval/list',
          meta: {
            title: '申报信息批复',
            requiresAuth: true
          }
        },
        {
          path: '/account-management/list',
          meta: {
            title: '账号管理',
            requiresAuth: true
          }
        },
        {
          path: '/reconsideration-declaration/list',
          meta: {
            title: '核验报告',
            requiresAuth: true
          }
        }
      ]
    }
  },
  computed: {
    ...mapGetters(['sidebar']),
    isCollapse() {
      return !this.sidebar.opened
    },
    activeMenu() {
      const { meta, path } = this.$route
      // 如果设置了高亮路径，则使用设置的路径
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    variables() {
      return {
        menuBg: '#304156',
        menuText: '#bfcbd9',
        menuActiveText: '#409EFF'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.sidebar-container {
  height: 100%;
  background-color: #304156;
  transition: all 0.3s ease-in-out;

  .el-scrollbar {
    height: 100%;
  }
  .scrollbar-wrapper {
    overflow-x: hidden !important;
  }

  .el-scrollbar__view {
    height: 100%;
  }

  .el-menu {
    border: none;
    height: 100%;
    width: 100% !important;
    background-color: #fff !important;
  }

  // 折叠菜单时的样式
  .el-menu--collapse {
    .el-submenu__title {
      span {
        display: none;
      }

      .el-submenu__icon-arrow {
        display: none;
      }
    }
  }

  // 图标样式
  i[class^='el-icon-'] {
    margin-right: 10px;
    font-size: 18px;
    vertical-align: middle;
  }
}
</style>

