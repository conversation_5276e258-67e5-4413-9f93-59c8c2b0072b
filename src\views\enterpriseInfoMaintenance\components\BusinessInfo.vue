<template>
    <div class="basic-info">
      <el-form label-position="top" :model="editableData" :rules="rules" ref="editableData">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="info-item">
              <div class="label">纳税人资质:</div>
              <el-form-item prop="taxpayerQualification">
                <div v-if="!isEdit" class="value">{{ enterpriseData.taxpayerQualification || "/" }}</div>
                <div v-else class="value">
                    <el-input v-model="editableData.taxpayerQualification" placeholder="请输入纳税人资质"></el-input> 
                </div>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="label">进出口企业代码:</div>
              <el-form-item prop="importExportCode">
                <div v-if="!isEdit" class="value">{{ enterpriseData.importExportCode || "/" }}</div>
                <div v-else class="value">
                    <el-input v-model="editableData.importExportCode" placeholder="请输入进出口企业代码"></el-input>
                </div>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="label">海关编码：</div>
              <el-form-item prop="customsCode">
                <div v-if="!isEdit" class="value">{{ enterpriseData.customsCode || "/" }}</div>
                <div v-else class="value">
                    <el-input v-model="editableData.customsCode" placeholder="请输入海关编码"></el-input>
                </div>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="label"><span v-if="isEdit" class="required-star">*</span>开户银行：</div>
              <el-form-item prop="openingBank">
                <div v-if="!isEdit" class="value">{{ enterpriseData.openingBank || "/" }}</div>
                <div v-else class="value">
                    <el-input v-model="editableData.openingBank" placeholder="请输入开户银行"></el-input>
                </div>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="info-item">
              <div class="label"><span v-if="isEdit" class="required-star">*</span>银行账号：</div>
              <el-form-item prop="bankAccount">
                <div v-if="!isEdit" class="value">{{ enterpriseData.bankAccount  || "/" }}</div>
                <div v-else class="value">
                    <el-input v-model="editableData.bankAccount" placeholder="请输入银行账号"></el-input>
                </div>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="label">年营业额：</div>
              <el-form-item prop="annualRevenue">
                <div v-if="!isEdit" class="value">{{ enterpriseData.annualRevenue || "/" }}</div>
                <div v-else class="value">
                    <el-input v-model="editableData.annualRevenue" placeholder="请输入年营业额"></el-input>
                </div>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="label">员工人数：</div>
              <el-form-item prop="employeeCount">
                <div v-if="!isEdit" class="value">{{ enterpriseData.employeeCount || "/" }}</div>
                <div v-else class="value">
                    <el-input v-model="editableData.employeeCount" placeholder="请输入员工人数"></el-input>
                </div>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </template>
  
  <script>
  export default {
    props: {
      enterpriseData: {
        type: Object,
        required: true,
      },
      businessInfoFormData: {
        type: Object,
        required: true,
        },
      isEdit: {
        type: Boolean,
        default: false,
      },
    },
    data() {
        return {
          editableData: {},
          rules: {
            openingBank: [
              { required: true, message: '开户银行不能为空', trigger: 'blur' },
            ],
            bankAccount: [
              { required: true, message: '银行账号不能为空', trigger: 'blur' },
            ],
          }
      };
    },
    watch: {
      businessInfoFormData: {
        handler(newVal) {
            this.editableData = { ...newVal };
        },
        deep: true,
        },
    },
    created() {
        this.editableData = { ...this.businessInfoFormData };
    },
    methods: {
      getFormData() {
        return this.editableData;
      },
        handleSave() {
            this.$emit('save', this.editableData);
        },
    },
  };
  </script>
  
  <style scoped>
  .basic-info{
    margin-top: 10px;
  }
  .basic-info .info-item {
    margin-bottom: 30px;
  }
  
  .basic-info .label {
    color: #515A6E;
    font-weight: 400;
    font-size: 14px;
  }
  
  .basic-info .value {
    margin-top: 10px;
    color: #17233D;
    font-weight: 400;
    font-size: 14px;
  }
  .required-star{
    color: red;
    margin-right: 5px;
  }
  </style>