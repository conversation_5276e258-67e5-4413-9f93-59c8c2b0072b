import apiClient from '@/utils/request'

//登录
export function loginApi(data) {
  return apiClient.post('/auth/login', data)
}

//获取扫码登录二维码
export function getQrCodeApi() {
  return apiClient.get('/auth/qrCode')
}

//轮询获取航贸通APP扫码授权登录结果
export function qrCodeLoginApi(param) {
  return apiClient.get('/auth/qrCode/login',{ params : param})
}

//获取图片验证码
export function getCodeImg() {
  return apiClient.get('/code')
}

//忘记密码邮箱验证
export function sendEmail(params) {
  return apiClient.get('/system/user/password/cert/send', {params:params})
}

//忘记密码身份验证
export function confirmPassword(data) {
  return apiClient.post('/system/user/password/cert/confirm', data)
}

//忘记密码重置密码
export function resetPassword(data) {
  return apiClient.post('/system/user/resetPassword', data)
}



