<template>
    <div class="basic-info">
      <el-form label-position="top" :model="editableData" :rules="rules" ref="editableData">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="info-item">
            <div class="label">注册地址:</div>
            <el-form-item prop="registrationAddress">
              <div v-if="!isEdit" class="value">{{ bankInfo.registrationAddress || "/" }}</div>
              <div v-else class="value">
                  <el-input v-model.trim="editableData.registrationAddress" placeholder="请输入注册地址"></el-input> 
              </div>
            </el-form-item>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <div class="label">办公地址:</div>
            <el-form-item prop="officeAddress">
              <div v-if="!isEdit" class="value">{{ bankInfo.officeAddress  || "/" }}</div>
              <div v-else class="value">
                  <el-input v-model.trim="editableData.officeAddress " placeholder="请输入办公地址"></el-input>
              </div>
            </el-form-item>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <div class="label"><span v-if="isEdit" class="required-star">*</span>联系人：</div>
            <el-form-item prop="contactPerson">
              <div v-if="!isEdit" class="value">{{ bankInfo.contactPerson  || "/" }}</div>
              <div v-else class="value">
                  <el-input v-model.trim="editableData.contactPerson" placeholder="请输入联系人"></el-input>
              </div>
            </el-form-item>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <div class="label"><span v-if="isEdit" class="required-star">*</span>联系电话：</div>
            <el-form-item prop="contactPhone">
              <div v-if="!isEdit" class="value">{{ bankInfo.contactPhone  || "/" }}</div>
              <div v-else class="value">
                  <el-input v-model.trim="editableData.contactPhone " placeholder="请输入联系电话"></el-input>
              </div>
            </el-form-item>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="info-item">
            <div class="label"><span v-if="isEdit" class="required-star">*</span>电子邮箱：</div>
            <el-form-item prop="email">
              <div v-if="!isEdit" class="value">{{ bankInfo.email  || "/" }}</div>
              <div v-else class="value">
                  <el-input v-model.trim="editableData.email " placeholder="请输入电子邮箱"></el-input>
              </div>
            </el-form-item>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <div class="label">邮政编码：</div>
            <el-form-item prop="postalCode">
              <div v-if="!isEdit" class="value">{{ bankInfo.postalCode  || "/" }}</div>
              <div v-else class="value">
                  <el-input v-model.trim="editableData.postalCode" placeholder="请输入邮政编码"></el-input>
              </div>
            </el-form-item>
          </div>
        </el-col>
      </el-row>
    </el-form>
    </div>
  </template>
  
  <script>
    import { verifyCompanyNumber,verifyEmail } from '@/assets/js/common'
  export default {
    props: {
      bankInfo: {
        type: Object,
        required: true,
      },
      contactInfoFormData: {
        type: Object,
        required: true,
        },
      isEdit: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        editableData: {},
        rules: {
          contactPerson: [
            { required: true, message: '请输入联系人', trigger: 'blur' },
            { max: 20, message: '银行名称不能超过20个字符', trigger: 'blur' }
          ],
          contactPhone: [
            { required: true, message: '请输入联系电话', trigger: 'blur' },
            { validator: verifyCompanyNumber, trigger: 'blur', }
          ],
          email: [
            { required: true, message: '请输入电子邮箱', trigger: 'blur' },
            { validator: verifyEmail, trigger: 'blur', }
          ],
          postalCode: [
            // { required: true, message: '请输入邮政编码', trigger: 'blur' },
            {
              pattern: /^[0-9]{6}$/,
              message: '邮政编码应为6位数字',
              trigger: 'blur',
            }
          ]
        }
    };
    },
    watch: {
      contactInfoFormData: {
          handler(newVal) {
              this.editableData = { ...newVal };
          },
          deep: true,
        },
    },
    created() {
        this.editableData = { ...this.contactInfoFormData };
    },
    methods: {
      getFormData() {
        return this.editableData;
      },
        handleSave() {
            this.$emit('save', this.editableData);
        },
    },
  };
  </script>
  
  <style scoped>
  .basic-info{
    margin-top: 10px;
  }
  .basic-info .info-item {
    margin-bottom: 30px;
  }
  
  .basic-info .label {
    color: #515A6E;
    font-weight: 400;
    font-size: 14px;
  }
  
  .basic-info .value {
    margin-top: 10px;
    color: #17233D;
    font-weight: 400;
    font-size: 14px;
  }
  .required-star{
    color: red;
    margin-right: 5px;
  }
  </style>