<template>
    <div class="basic-info">
      <el-form label-position="top" :model="editableData" :rules="rules" ref="editableData">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="info-item">
            <div class="label">注册地址:</div>
            <div v-if="!isEdit" class="value">{{ bankInfo.registrationAddress || "/" }}</div>
            <div v-else class="value">
                <el-input v-model="editableData.registrationAddress" placeholder="请输入注册地址"></el-input> 
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <div class="label">办公地址:</div>
            <div v-if="!isEdit" class="value">{{ bankInfo.officeAddress  || "/" }}</div>
            <div v-else class="value">
                <el-input v-model="editableData.officeAddress " placeholder="请输入办公地址"></el-input>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <div class="label"><span v-if="isEdit" class="required-star">*</span>联系人：</div>
            <div v-if="!isEdit" class="value">{{ bankInfo.contactPerson  || "/" }}</div>
            <div v-else class="value">
                <el-input v-model="editableData.contactPerson" placeholder="请输入联系人"></el-input>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <div class="label"><span v-if="isEdit" class="required-star">*</span>联系电话：</div>
            <div v-if="!isEdit" class="value">{{ bankInfo.contactPhone  || "/" }}</div>
            <div v-else class="value">
                <el-input v-model="editableData.contactPhone " placeholder="请输入联系电话"></el-input>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="info-item">
            <div class="label"><span v-if="isEdit" class="required-star">*</span>电子邮箱：</div>
            <div v-if="!isEdit" class="value">{{ bankInfo.email  || "/" }}</div>
            <div v-else class="value">
                <el-input v-model="editableData.email " placeholder="请输入电子邮箱"></el-input>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <div class="label">邮政编码：</div>
            <div v-if="!isEdit" class="value">{{ bankInfo.postalCode  || "/" }}</div>
            <div v-else class="value">
                <el-input v-model="editableData.postalCode" placeholder="请输入邮政编码"></el-input>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-form>
    </div>
  </template>
  
  <script>
  export default {
    props: {
      bankInfo: {
        type: Object,
        required: true,
      },
      contactInfoFormData: {
        type: Object,
        required: true,
        },
      isEdit: {
        type: Boolean,
        default: false,
      },
    },
    data() {
        return {
          editableData: {},
          rules: {
            contactPerson: [
              { required: true, message: '请输入联系人', trigger: 'blur' },
            ],
            contactPhone: [
              { required: true, message: '请输入联系电话', trigger: 'blur' },
              {
                pattern: /^1\d{10}$/,
                message: '联系电话格式错误',
                trigger: 'blur',
              },
            ],
            email: [
              { required: true, message: '请输入电子邮箱', trigger: 'blur' },
              {
                type: 'email',
                message: '请输入正确的邮箱格式',
                trigger: ['blur', 'change'],
              },
            ],
            postalCode: [
              { required: true, message: '请输入邮政编码', trigger: 'blur' },
              {
                pattern: /^[0-9]{6}$/,
              }
            ]
          }
      };
    },
    watch: {
      contactInfoFormData: {
          handler(newVal) {
              this.editableData = { ...newVal };
          },
          deep: true,
        },
    },
    created() {
        this.editableData = { ...this.contactInfoFormData };
    },
    methods: {
      getFormData() {
        return this.editableData;
      },
        handleSave() {
            this.$emit('save', this.editableData);
        },
    },
  };
  </script>
  
  <style scoped>
  .basic-info{
    margin-top: 10px;
  }
  .basic-info .info-item {
    margin-bottom: 30px;
  }
  
  .basic-info .label {
    color: #515A6E;
    font-weight: 400;
    font-size: 14px;
  }
  
  .basic-info .value {
    margin-top: 10px;
    color: #17233D;
    font-weight: 400;
    font-size: 14px;
  }
  .required-star{
    color: red;
    margin-right: 5px;
  }
  </style>