<template>
   <div>
    <el-upload
      :action="action"
      :headers="headers"
      :file-list="fileList"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-remove="handleRemove"
      :limit="limit"
      :on-preview="handlePreview"
    >
      <el-button size="small" type="primary">点击上传</el-button>
      <div slot="tip" class="el-upload__tip">只能上传 jpg,png,pdf 文件，且不超过 500kb</div>
    </el-upload>
  
    <!-- 预览弹窗 -->
    <el-dialog :visible.sync="previewDialogVisible" append-to-body>
      <div :class="imageType != 'application/pdf' ? 'img-preview' : 'pdf-preview'">
        <img v-if="imageType != 'application/pdf'" :src="previewUrl" style="width: 100%; height: auto;" />
        <object
          v-else
          :data="previewUrl"
          type="application/pdf"
          width="100%"
          height="600px"
        ></object>
      </div>
    </el-dialog>
   </div>
  </template>
  
  <script>
    import { downloadApi } from '@/apis/upload';
  export default {
    name: 'FileUpload',
    props: {
      fileList: {
        type: Array,
        default: () => []
      },
      action: {
        type: String,
        required: true
      },
      headers: {
        type: Object,
        default: () => ({})
      },
      limit: {
        type: Number,
        default: 8
      }
    },
    watch: {
      fileList: {
        handler(newVal) {
            console.log(newVal,'------');
          },
          deep: true,
      }
    },
    data() {
      return {
        previewDialogVisible: false,
        previewUrl: '',
        arrayList: [],
        imageType:'',
      };
    },
    methods: {
      beforeUpload(file) {
        const suffixName = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
        const isLimit = file.size/1024  < 5*1024
        if (suffixName !== 'jpg' && suffixName !== 'png' && suffixName !== 'jpeg' && suffixName !== 'pdf') {
          this.$message.error('仅支持上传PNG、JPG和PDF格式文件')
          return false
        }
        if (!isLimit) {
          this.$message.error('单个文件大小不能超过5M')
          return false
        }
        //pdf 处理
        if(suffixName === 'pdf'){
          let reader = new FileReader()
          reader.readAsDataURL(file)
          reader.onload = ()=>{
            file.fileData = reader.result
            return true
          }
        }else{
          return true
        }
      },
      handleSuccess(response, file, fileList) {
        console.log(fileList,'fileList');
        if(response.message === "File is damaged, please upload again"){
          this.$message.error(response.message);
          this.$nextTick(()=>{
            this.arrayList = fileList.filter(item=>item.uid!==file.uid);
          })
          return;
        }
        this.$emit('input', fileList.map(item => ({
          fileName: item.fileName || item.response?.data?.docName || item.name,
          annexId: item.annexId || item.response?.data?.id,
          name: item.fileName || item.response?.data?.docName || item.name,
        })));
      },
      handleError() {
        this.$message.error('上传失败，请重试');
      },
      handleRemove(file, fileList) {
        console.log(fileList,'fileList');
        this.$emit('input', fileList.map(item => ({
          fileName: item.response?.data?.docName,
          annexId: item.response?.data?.id,
          name: item.fileName || item.response?.data?.docName || item.name,
        })));
      },
      async handlePreview(file) {
        let proRes = await downloadApi({ id: file.annexId });
        if (proRes.type === 'application/pdf') {
          const blob = new Blob([proRes], { type: 'application/pdf' }); // 创建 Blob 对象
          this.previewUrl = URL.createObjectURL(blob); // 转换为临时访问地址
        }else if (proRes.type === 'image/jpeg' || proRes.type === 'image/png') {
          const blob = new Blob([proRes], { type: 'image/jpeg' }); // 创建 Blob 对象
          this.previewUrl = URL.createObjectURL(blob); // 转换为临时访问地址
        }
        this.imageType = proRes.type
        this.previewDialogVisible = true;
      }
    }
  };
  </script>
  
  <style scoped>
  .upload-file-item {
    position: relative;
    width: 100%;
    height: 100%;
  }
  
  .el-upload-list__item-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  .el-dialog__header {
    padding: 25px;
  }
  .el-dialog__headerbtn {
    top:12px
  }
  .img-preview{
    height: 100%;
  }
  .pdf-preview{
    height: 600px;
  }
  </style>