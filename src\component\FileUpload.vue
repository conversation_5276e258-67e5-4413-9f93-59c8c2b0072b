<template>
   <div>
    <el-upload
      :action="action"
      :headers="headers"
      :file-list="fileList"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-remove="handleRemove"
      :limit="limit"
      :on-preview="handlePreview"
    >
      <el-button size="small" type="primary">点击上传</el-button>
      <div slot="tip" class="el-upload__tip">只能上传 jpg,png,pdf 文件，且不超过 500kb</div>
    </el-upload>
  
    <!-- 预览弹窗 -->
    <el-dialog :visible.sync="previewDialogVisible" append-to-body>
      <div :class="previewUrl.indexOf('application/pdf') === -1 ? 'img-preview' : 'pdf-preview'">
        <img v-if="previewUrl.indexOf('application/pdf') === -1" :src="previewUrl" style="width: 100%; height: auto;" />
        <object
          v-else
          :data="previewUrl"
          type="application/pdf"
          width="100%"
          height="100%"
        ></object>
      </div>
    </el-dialog>
   </div>
  </template>
  
  <script>
  export default {
    name: 'FileUpload',
    props: {
      fileList: {
        type: Array,
        default: () => []
      },
      action: {
        type: String,
        required: true
      },
      headers: {
        type: Object,
        default: () => ({})
      },
      limit: {
        type: Number,
        default: 8
      }
    },
    data() {
      return {
        previewDialogVisible: false,
        previewUrl: '',
        arrayList:[],
      };
    },
    methods: {
      beforeUpload(file) {
        const suffixName = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
        const isLimit = file.size/1024  < 5*1024
        if (suffixName !== 'jpg' && suffixName !== 'png' && suffixName !== 'jpeg' && suffixName !== 'pdf') {
          this.$message.error('仅支持上传PNG、JPG和PDF格式文件')
          return false
        }
        if (!isLimit) {
          this.$message.error('单个文件大小不能超过5M')
          return false
        }
        //pdf 处理
        if(suffixName === 'pdf'){
          let reader = new FileReader()
          reader.readAsDataURL(file)
          reader.onload = ()=>{
            file.fileData = reader.result
            return true
          }
        }else{
          return true
        }
      },
      handleSuccess(response, file, fileList) {
        if(response.message === "File is damaged, please upload again"){
          this.$message.error(response.message);
          this.$nextTick(()=>{
            this.arrayList = fileList.filter(item=>item.uid!==file.uid);
          })
          return;
        }
        this.arrayList = fileList.map(item=>{
          let fileObj = {
            docName: item.name || item.docName,
            docDownloadUrl: (item.response&&item.response?.data?.url)||item.docDownloadUrl,
            id:item.id || item.response?.data?.id,
            docDownloadUrlBase: item.url || item.docDownloadUrlBase,
          }
          if(item.raw && item.raw.type==='application/pdf'){
            fileObj.fileData = item.raw.fileData
          }
          return {
            ...item,
            ...fileObj
          }
        })
        this.$emit('input', this.arrayList.map(item => ({
          fileName: item.docName,
          annexId: item.id,
        })));
      },
      handleError() {
        this.$message.error('上传失败，请重试');
      },
      handleRemove(file, fileList) {
        console.log(fileList,'fileList');
        this.$emit('input', fileList.map(item => ({
          fileName: item.response?.data?.docName,
          annexId: item.response?.data?.id,
        })));
      },
      handlePreview(file) {
        if(file.raw.fileData){
          this.previewUrl = file.raw.fileData;
        }else{
          this.previewUrl = file.url || file.docDownloadUrlBase || file.response.data.url;
        }
        this.previewDialogVisible = true;
      }
    }
  };
  </script>
  
  <style scoped>
  .upload-file-item {
    position: relative;
    width: 100%;
    height: 100%;
  }
  
  .el-upload-list__item-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  .el-dialog__headerbtn {
    top:12px
  }
  .img-preview{
    height: 100%;
  }
  .pdf-preview{
    height: 600px;
  }
  </style>