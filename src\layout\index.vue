<template>
  <div class="app-wrapper">
    <!-- 顶部导航栏 -->
    <navbar />

    <div class="main-container" :class="{ 'sidebar-collapse': isCollapse }">
      <!-- 左侧菜单栏 -->
      <sidebar class="sidebar-container" :class="{ 'is-collapsed': isCollapse }" />

      <div class="main-content">
        <!-- 面包屑导航 -->
        <!-- <div class="breadcrumb-container">
          <breadcrumb />
        </div> -->

        <!-- 标签栏 -->
        <!-- <div class="tabs-view-container">
          <tabs-view />
        </div> -->

        <!-- 主要内容区域 -->
        <app-main />
      </div>
    </div>
  </div>
</template>

<script>
import Navbar from './components/Navbar'
import Sidebar from './components/Sidebar'
import AppMain from './components/AppMain'
// import Breadcrumb from './components/Breadcrumb'
// import TabsView from './components/TabsView'

export default {
  name: 'Layout',
  components: {
    Navbar,
    Sidebar,
    AppMain,
    // Breadcrumb,
    // TabsView
  },
  computed: {
    isCollapse() {
      return !this.$store.state.app.sidebar.opened
    }
  }
}
</script>

<style lang="scss" scoped>
.app-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
}

.main-container {
  height: calc(100% - 60px);
  margin-left: 210px;
  position: relative;
  &.sidebar-collapse {
    margin-left: 64px;
  }
}

.sidebar-container {
  position: fixed;
  top: 60px;
  bottom: 0;
  left: 0;
  width: 210px;
  overflow: hidden;
  background-color: #ffffff;
  transition: width 0.28s;
  z-index: 1001;
  &.is-collapsed {
    width: 64px;
    .el-menu--collapsed {
      width: 64px;
    }
  }
}

.main-content {
  padding: 10px;
  height: 100%;
  position: relative;
}

.breadcrumb-container {
  padding: 8px 10px;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  margin-bottom: 10px;
}

.tabs-view-container {
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  margin-bottom: 10px;
}
</style>

