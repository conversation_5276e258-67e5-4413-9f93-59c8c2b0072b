<template>
    <div class="basic-info">
      <el-form label-position="top" :model="editableData" ref="editableData">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="info-item">
              <div class="label">业务范围:</div>
              <div v-if="!isEdit" class="value">{{ bankInfo.mainBusiness  || "/" }}</div>
              <div v-else class="value">
                  <el-input
                      type="textarea"
                      :rows="6"
                      placeholder="请输入内容"
                      v-model="editableData.mainBusiness">
                  </el-input>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="label">SWIFT代码:</div>
              <div v-if="!isEdit" class="value">{{ bankInfo.swiftCode || "/" }}</div>
              <div v-else class="value">
                  <el-input v-model="editableData.swiftCode" placeholder="请输入SWIFT代码"></el-input>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="label">清算行号：</div>
              <div v-if="!isEdit" class="value">{{ bankInfo.clearingBankNumber || "/" }}</div>
              <div v-else class="value">
                  <el-input v-model="editableData.clearingBankNumber" placeholder="请输入清算行号"></el-input>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="label">外汇业务资质：</div>
              <div v-if="!isEdit" class="value">{{ bankInfo.foreignExchangeQualification || "/" }}</div>
              <div v-else class="value">
                  <el-input v-model="editableData.foreignExchangeQualification" placeholder="请输入外汇业务资质"></el-input>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="info-item">
              <div class="label">跨境人民币业务资质：</div>
              <div v-if="!isEdit" class="value">{{ bankInfo.crossBorderRmbQualification || "/" }}</div>
              <div v-else class="value">
                  <el-input v-model="editableData.crossBorderRmbQualification" placeholder="请输入跨境人民币业务资质"></el-input>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </template>
  
  <script>
  export default {
    props: {
      bankInfo: {
        type: Object,
        required: true,
      },
      businessInfoFormData: {
        type: Object,
        required: true,
        },
      isEdit: {
        type: Boolean,
        default: false,
      },
    },
    data() {
        return {
            editableData: {},
      };
    },
    watch: {
      businessInfoFormData: {
        handler(newVal) {
          this.editableData = { ...newVal };
            console.log(this.editableData,'this.editableData');
            
        },
        deep: true,
        },
    },
    mounted() {
      this.editableData = { ...this.businessInfoFormData };
        console.log(this.editableData,'this.editableData');
        
    },
    methods: {
      getFormData() {
        return this.editableData;
      },
        handleSave() {
            this.$emit('save', this.editableData);
        },
    },
  };
  </script>
  
  <style scoped>
  .basic-info{
    margin-top: 10px;
  }
  .basic-info .info-item {
    margin-bottom: 30px;
  }
  
  .basic-info .label {
    color: #515A6E;
    font-weight: 400;
    font-size: 14px;
  }
  
  .basic-info .value {
    margin-top: 10px;
    color: #17233D;
    font-weight: 400;
    font-size: 14px;
  }
  .required-star{
    color: red;
    margin-right: 5px;
  }
  </style>