import {
  getTradeStatusParamApi,
  getContractTypeParamApi,
  getCurrencyTypeParamApi,
  getSettlementDirectionParamApi
} from '@/apis/enum'

export default {
  namespaced: true,
  state: {
    tradeStatusParam: [],
    contractTypeParam: [],
    currencyTypeParam: [],
    settlementDirectionParam: []
  },
  mutations: {
    SET_TRADE_STATUS_ENUM(state, data) {
      state.tradeStatusParam = data
    },
    SET_CONTRACT_TYPE_ENUM(state, data) {
      state.contractTypeParam = data
    },
    SET_CURRENCY_TYPE_ENUM(state, data) {
      state.currencyTypeParam = data
    },
    SET_SETTLEMENT_DIRECTION_ENUM(state, data) {
      state.settlementDirectionParam = data
    }
  },
  actions: {
    async getTradeStatusParam({ commit }) {
      const res = await getTradeStatusParamApi()
      commit('SET_TRADE_STATUS_ENUM', res.data?.details || [])
    },
    async getContractTypeParam({ commit }) {
      const res = await getContractTypeParamApi()
      commit('SET_CONTRACT_TYPE_ENUM', res.data?.details || [])
    },
    async getCurrencyTypeParam({ commit }) {
      const res = await getCurrencyTypeParamApi()
      commit('SET_CURRENCY_TYPE_ENUM', res.data?.details || [])
    },
    async getSettlementDirectionParam({ commit }) {
      const res = await getSettlementDirectionParamApi()
      commit('SET_SETTLEMENT_DIRECTION_ENUM', res.data?.details || [])
    }
  }
}
