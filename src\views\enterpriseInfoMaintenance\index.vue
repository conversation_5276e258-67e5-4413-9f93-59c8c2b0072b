<template>
  <div>
    <h3>企业信息维护</h3>
    <div class="enterprise-list">
      <!-- 顶部表单区域 -->
      <el-form :inline="true" class="demo-form-inline" label-position="top">
        <el-form-item label="企业名称">
          <el-input v-model="queryForm.enterpriseName" placeholder="请输入企业名称"></el-input>
        </el-form-item>
        <el-form-item label="统一社会信用代码">
          <el-input v-model="queryForm.creditCode" placeholder="请输入统一社会信用代码"></el-input>
        </el-form-item>
        <el-form-item label="企业类型">
          <el-select v-model="queryForm.enterpriseType" clearable placeholder="请选择企业类型">
            <el-option v-for="(item,index) in enterpriseTypeList" 
              :key="index"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item style="margin-top: 40px;">
          <el-button type="primary" @click="getTableData">查询</el-button>
        </el-form-item>
      </el-form>
  
      <!-- 表格区域 -->
      <div>
        <div class="batch-opt">
          <el-button type="primary" @click="handleAddCompany">
            新增
          </el-button>
        </div>
        <el-table
          border
          empty-text
          class="table_Header"
          :data="tableData" >
          <el-table-column prop="companyName" label="企业名称" show-overflow-tooltip width="180"></el-table-column>
          <el-table-column prop="creditCode" show-overflow-tooltip  label="统一社会信用代码" width="180"></el-table-column>
          <el-table-column prop="legalRepresentative" show-overflow-tooltip  label="法定代表人" width="100"></el-table-column>
          <el-table-column prop="establishmentDate" show-overflow-tooltip  label="成立日期" width="160"></el-table-column>
          <el-table-column prop="companyTypeName" show-overflow-tooltip  label="企业类型" width="120"></el-table-column>
          <el-table-column prop="industry" label="所属行业" show-overflow-tooltip width="180" ></el-table-column>
          <el-table-column prop="contactPerson"  show-overflow-tooltip width="120" label="联系人"></el-table-column>
          <el-table-column prop="contactPhone" show-overflow-tooltip  label="联系电话" width="160"></el-table-column>
          <el-table-column prop="email" label="电子邮箱" show-overflow-tooltip  width="180"></el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template slot-scope="scope">
              <el-tooltip content="详情" placement="top">
                <img
                  class="table-icon"
                  :src="require('@/assets/images/view.png')"
                  alt=""
                  @click="viewDetail(scope.row)"
                />
              </el-tooltip>
              <el-tooltip content="编辑" placement="top">
                <img
                  class="table-icon"
                  :src="require('@/assets/images/edit.png')"
                  alt=""
                  @click="editCompany(scope.row)"
                />
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <img
                  class="table-icon"
                  :src="require('@/assets/images/delete.png')"
                  alt=""
                  @click="deleteCompany(scope.row)"
                />
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-wrapper">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total">
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
  
<script>
  import { dictEnterpriseType } from '@/apis/enum'
  import { getCompanyListApi,deleteCompanyApi } from '@/apis/index'
    
  export default {
    data() {
      return {
        queryForm: {
          enterpriseName: '',
          creditCode: '',
          enterpriseType: ''
        },
        enterpriseTypeList:[],
        tableData: [],
        currentPage: 1,
        pageSize: 10,
        total: 0
      };
    },
    async mounted() {
      const res = await dictEnterpriseType();
      this.enterpriseTypeList = res.data.details
      await this.getTableData()
    },
    methods: {
      async getTableData() {
        // 获取表格数据逻辑
        const res =  await getCompanyListApi({
          pageNum: this.currentPage,
          pageSize: this.pageSize,
          companyName: this.queryForm.enterpriseName,
          code: this.queryForm.creditCode,
          companyType: this.queryForm.enterpriseType,
          roleType:'102'
        })
        this.tableData = res.data.rows
        this.total = res.data.total
      },
      handleAddCompany() {
        this.$router.push({
          name: 'companyDetail',
          query: {
            isAdd: true,
          }
        });
      },
      viewDetail(row) {
        this.$router.push({
          name: 'companyDetail',
          query: {
            id: row.id,
          }
        });
      },
      editCompany(row) {
        this.$router.push({
          name: 'companyDetail',
          query: {
            id: row.id,
            isEdit: true,
          }
        });
      },
      deleteCompany(row) {
        console.log(row, "row");
        this.$confirm(`是否确认删除`+ row.companyName +"企业？" ,'提示', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          const params = {
            id: row.id
          }
          const res = await deleteCompanyApi(params)
          if (res.code == 200) {
            this.$message.success('删除成功')
            this.getTableData()
          }
        }).catch(() => {
        });
      },
      handleSizeChange(val) {
        this.pageSize = val;
        this.getTableData();
      },
      handleCurrentChange(val) {
        this.currentPage = val;
        this.getTableData();
      },
    }
  };
  </script>
  
  <style scoped>
  .enterprise-list {
    padding: 20px;
    border-radius: 8px;
    background-color: #FFFFFF;
    margin-top: 20px;
    }
  .demo-form-inline {
    margin-bottom: 20px;
    display: flex;
    align-items: flex-end;
  }
  .table-icon{
    width: 24px;
  }
  .batch-opt{
    background-color: #FAFBFC;
    border: 1px solid #EDEDED;
    border-bottom:none;
    padding: 10px 30px;
    text-align: end;
  }

  </style>
  <style lang="scss" scoped>
    /deep/ .el-table__body-wrapper {
      min-height: 480px;
}
</style>