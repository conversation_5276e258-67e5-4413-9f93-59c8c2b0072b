# ESLint 配置指南

本文档说明如何在此 Vue 2 项目中使用 ESLint 进行代码质量检查。

## ESLint 配置

本项目使用 ESLint 进行代码质量检查，配置了以下规则：

- Vue 推荐规则（plugin:vue/recommended）
- ESLint 推荐规则（eslint:recommended）
- Prettier 集成（prettier）

## 可用的命令

- `npm run lint` - 运行 ESLint 检查代码
- `npm run lint:fix` - 运行 ESLint 并自动修复问题
- `npm run lint:style` - 运行 ESLint 并修复缩进和引号问题
- `npm run lint:report` - 生成 HTML 格式的 ESLint 报告

## 主要规则说明

### Vue 规则

- `vue/html-indent`: 使用 2 个空格缩进
- `vue/html-self-closing`: 要求自闭合标签
- `vue/max-attributes-per-line`: 单行最多 3 个属性，多行每行 1 个属性
- `vue/require-default-prop`: 要求 props 有默认值
- `vue/require-prop-types`: 要求 props 有类型定义
- `vue/attributes-order`: 要求属性顺序一致
- `vue/order-in-components`: 要求组件选项顺序一致

### JavaScript 规则

- `indent`: 使用 2 个空格缩进
- `linebreak-style`: 使用 Windows 换行符（CRLF）
- `quotes`: 使用单引号
- `semi`: 不使用分号
- `no-console`: 警告使用 console
- `no-debugger`: 警告使用 debugger
- `no-unused-vars`: 警告未使用的变量
- `prefer-const`: 优先使用 const
- `no-var`: 不使用 var

### Prettier 集成

项目集成了 Prettier 来格式化代码，配置如下：

```json
{
  "singleQuote": true,
  "semi": false,
  "trailingComma": "none",
  "endOfLine": "crlf",
  "printWidth": 100,
  "tabWidth": 2
}
```

## 忽略文件

项目使用 .eslintignore 文件来忽略特定文件和目录：

- /dist/
- /node_modules/
- /public/
- *.min.js
- 配置文件（.eslintrc.js, babel.config.js 等）

## 编辑器集成

推荐在编辑器中安装 ESLint 和 Prettier 插件，以便在编写代码时获得实时反馈。

### VS Code

1. 安装 ESLint 扩展
2. 安装 Prettier 扩展
3. 在设置中启用保存时自动修复：

```json
{
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode"
}
```

## 常见问题解决

### 行尾问题

如果遇到行尾（linebreak-style）相关的错误，可以：

1. 确保 .prettierrc 中设置了 `"endOfLine": "crlf"`
2. 在 Git 配置中设置 `core.autocrlf=true`

### 与 Prettier 冲突

如果 ESLint 规则与 Prettier 冲突，可以：

1. 检查 eslint-config-prettier 是否正确配置
2. 确保 Prettier 规则在 ESLint 配置的 extends 数组的最后一项
