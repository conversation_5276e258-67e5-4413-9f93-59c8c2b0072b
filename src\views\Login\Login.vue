
<template>
  <div class="login-container">
    <div class="login-card">
      <div class="left-section">
        <div style="width: 100%;">
          <div style="margin: 50px;">
            <h2 class="title">舟山离岸贸易信息服务平台</h2>
            <p class="description">
              为离岸贸易企业提供便捷的服务，<br />
              助力企业降低运营成本，<br />
              提升贸易效率。
            </p>
          </div>
          <img src="@/assets/images/login.png" alt="login" style="width: 310px;" />
        </div>
      </div>
      <div class="right-section">
        <h3 class="welcome-title">欢迎登入</h3>
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <el-tab-pane label="账号密码登录" name="password-login">
              <el-form ref="loginForm" :model="loginForm" :rules="rules" label-position="top">
            <el-form-item label="用户名" prop="username">
              <el-input v-model="loginForm.username" placeholder="请输入用户名"></el-input>
            </el-form-item>
            <el-form-item label="密码" prop="password">
              <el-input v-model="loginForm.password" placeholder="请输入密码" show-password></el-input>
            </el-form-item>
            <el-form-item label="验证码" prop="captcha">
              <el-row :gutter="20">
                <el-col :span="16">
                  <el-input v-model="loginForm.captcha" placeholder="请输入验证码"></el-input>
                </el-col>
                <el-col :span="8" class="login-code">
                  <img style="width: 100%; height: 100%;" :src="codeUrl" @click="getCode">
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item>
              <el-button type="text" class="forgot-password" @click="toForgetPass">忘记密码？</el-button>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" :loading="loading" class="login-btn" @click="handleLogin">登录</el-button>
            </el-form-item>
          </el-form>
          </el-tab-pane>
          <el-tab-pane label="扫码登录" name="qr-login">
            <div class="qr-login-container">
              <div class="qr-code-container">
                <div class="qr-code-wrapper" :class="{ 'expired': isExpired }">
                  <QRCodes alt="" :url='qrCodeUrl' class="qr-code-img"></QRCodes>
                  <div v-if="isExpired" class="expired-overlay" @click="refreshQrCode">
                    <img src="@/assets/images/Refresh.png" alt="Refresh" style="width: 20px;">
                    <p>二维码已失效<br />请点击刷新</p>
                  </div>
                </div>
              </div>
              <p class="qr-code">请使用航贸通APP扫描二维码登录</p>
              <p class="qr-code2">二维码有效期为5分钟，请及时扫描</p>
            </div>
          </el-tab-pane>
        </el-tabs>
        
        <p class="browser-recommend">推荐使用Chrome、Firefox、Edge等现代浏览器访问系统</p>
      </div>
    </div>
    <el-dialog title="找回密码" :visible.sync="forgetPassDialogVisible" width="50%">
      <el-steps :active="activeStep" finish-status="success" align-center>
        <el-step title="验证身份" :class="{ 'is-process': activeStep === 0, 'is-finish': activeStep > 0 }"></el-step>
        <el-step title="重置密码" :class="{ 'is-process': activeStep === 1, 'is-finish': activeStep > 1 }"></el-step>
        <el-step title="完成" :class="{ 'is-process': activeStep === 2 }"></el-step>
      </el-steps>
      <div v-if="activeStep === 0" class="forget-form">
        <el-form :model="forgetForm" :rules="forgetRules" label-position="top" ref="forgetForm" style="width: 50%;">
          <el-form-item label="用户名" prop="username">
            <el-input v-model="forgetForm.username" placeholder="请输入用户名"></el-input>
          </el-form-item>
          <el-form-item label="验证码" prop="captcha">
            <div style="display: flex;">
              <el-input v-model="forgetForm.captcha" placeholder="请输入邮箱验证码" style="margin-right: 10px;"></el-input>
              <el-button type="primary" :disabled="isCounting" @click="sendCaptcha">{{ isCounting ? `重新发送(${countdown}s)` : '获取验证码' }}</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div v-else-if="activeStep === 1" class="forget-form">
        <el-form :model="resetForm" :rules="resetRules" label-position="top" ref="resetForm" style="width: 50%;">
          <el-form-item label="新密码" prop="newPassword">
            <el-input v-model="resetForm.newPassword" placeholder="请输入新密码" type="password"></el-input>
          </el-form-item>
          <el-form-item label="确认新密码" prop="confirmPassword">
            <el-input v-model="resetForm.confirmPassword" placeholder="请再次输入新密码" type="password"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div v-else-if="activeStep === 2" class="forget-form">
        <div class="success-message">
          <img src="@/assets/images/Icon_success.png" alt="Icon_success" style="width: 60px;">
          <div class="success-message-text">密码重置成功!</div>
          <p class="success-message-tips">您的密码已经重置完成</p>
          <div class="success-message-tips2">
            <p> ·  请使用新密码重新登录系统</p>
            <p> · 为了财产安全，请妥善保管您的密码</p>
            <p> ·  如遇问题，请联系系统管理员</p>
          </div>
        </div>
      </div>
      <!-- 其他步骤内容 -->
      <span slot="footer" class="dialog-footer">
        <el-button v-if="activeStep !== 2" @click="forgetPassDialogVisible = false">取 消</el-button>
        <el-button v-if="activeStep !== 2" type="primary" @click="nextStep">下一步</el-button>
        <el-button v-if="activeStep === 2" type="primary" @click="nextStep">完成</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getCodeImg, sendEmail,confirmPassword,resetPassword,getQrCodeApi } from '@/apis/auth'
import { encrypt } from '@/utils/slylLoginRsaEncrypt'
import QRCodes from "@/component/comQRCode.vue"
export default {
  components: {
    QRCodes,
  },
  data() {
    const validateNewPassword = (rule, value, callback) => {
      const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
      if (!regex.test(value)) {
        callback(new Error('密码为至少8位且区分大小字母、数字和特殊字符的组合，如AAbb11&22'));
      } else {
        callback();
      }
    };
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.resetForm.newPassword) {
        callback(new Error('两次输入密码不一致'));
      } else {
        callback();
      }
    };
    return {
      activeTab: 'password-login',
      loading:false,
      codeUrl: '',
      qrCodeUrl: '', // 默认二维码图片占位符
      isExpired: false, // 二维码是否过期
      expirationTime: null, // 二维码过期时间
      loginForm: {
        username: 'admin',
        password: 'Aa123456',
        captcha: '',
        uuid:''
      },
      rules: {
        username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
        captcha: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
      },
      forgetPassDialogVisible: false,
      activeStep: 0,
      countdown: 0,
      isCounting: false,
      forgetForm: {
        username: '',
        email: '',
        captcha: ''
      },
      forgetRules: {
        username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
        email: [{ required: true, message: '请输入邮箱', trigger: 'blur' }],
        captcha: [{ required: true, message: '请输入邮箱验证码', trigger: 'blur' }]
      },
      resetForm: {
        newPassword: '',
        confirmPassword: ''
      },
      resetRules: {
        newPassword: [{ required: true, message: '请输入新密码', trigger: 'blur' },
        { validator: validateNewPassword, trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请再次输入新密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      },
      userId: '',
      requestId:'',
    }
  },
  mounted() {
    this.getCode()
  },
  beforeDestroy() {
    if (this.countdownInterval) {
      clearInterval(this.countdownInterval);
    }
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  },
  methods: {
    getCode() {
      getCodeImg().then(res => {
        this.codeUrl = res.data.img
        this.loginForm.uuid = res.data.uuid
      })
    },
    generateQrCode() {
      // 模拟生成二维码
      getQrCodeApi().then(res => {
        if (res.code == 200) {
          this.qrCodeUrl = res.data.qrCodeUrl
          this.requestId = res.data.queryId
          this.getQrCodeLoginInfo()
          this.expirationTime = Date.now() + 5 * 60 * 1000; // 5分钟后过期
          this.startExpirationCheck();
          this.timeoutId = setTimeout(() => {
            console.log(this.intervalId, 'intervalId');
            clearInterval(this.intervalId)
            clearTimeout(this.timeoutId);
            this.intervalId = null;
          }, 300000);
        }
      })
    },
    startExpirationCheck() {
      setInterval(() => {
        if (Date.now() >= this.expirationTime) {
          this.isExpired = true;
        }
      }, 1000);
    },
    refreshQrCode() {
      this.generateQrCode();
      this.isExpired = false;
    },
    async handleTabClick() {
      if (this.activeTab === 'qr-login') {
        await this.generateQrCode()
      }
    },
    // 扫码获取登录信息
    getQrCodeLoginInfo() {
      const requestId = this.requestId
      const vm = this;
      const data = {
        queryId: requestId,
      };
      this.intervalId = setInterval(async () =>{
        try {
          const res = await vm.$store.dispatch('auth/qrLogin', data); // 使用 await 等待 action 返回结果
          if (res) {
            clearInterval(this.intervalId); // 登录成功停止轮询
            this.$message.success('扫码成功');
            this.$router.push('/enterpriseInfoMaintenance');
          }
        } catch (error) {
          console.error('扫码登录失败:', error);
        }
      }, 5000);
    },
    toForgetPass() {
      // 忘记密码处理逻辑
      this.forgetPassDialogVisible = true
    },
    handleLogin() {
      // 登录处理逻辑
      this.$refs.loginForm.validate(async (valid) => { 
        if (valid) {
          this.loading = true
          try {
            const data = {
              username: this.loginForm.username,
              password: encrypt(this.loginForm.password),
              code: this.loginForm.captcha,
              uuid: this.loginForm.uuid
            }
            const res = await this.$store.dispatch('auth/login', data)
            console.log(res,'======');
            if (res) {
              this.$message.success('登录成功')
              // const authType = res.data.userType;
              // switch (authType) {
              //   case 'admin':
              //     redirectPath = '/adminDashboard';
              //     break;
              //   case 'user':
              //     redirectPath = '/userDashboard';
              //     break;
              //   case 'guest':
              //     redirectPath = '/guestDashboard';
              //     break;
              //   default:
              //     redirectPath = '/enterpriseInfoMaintenance';
              // }
              this.$router.push('/enterpriseInfoMaintenance')
            }
          } catch (error) {
            this.$message.error(error.message || '登录失败，请重试')
          } finally {
            this.loading = false
          }
        } else {
          this.$message.warning('请填写正确的登录信息')
        }
      })
    },
    sendCaptcha() {
      if (this.isCounting) return;
      sendEmail({
        userName: this.forgetForm.username,
        eventType: 1
      }).then(res => {
        console.log(res, '-------');
        if (res.code === 200) {
          this.$message.success('验证码发送成功，请查收')
          this.countdown = 120;
          this.isCounting = true;
          const interval = setInterval(() => {
            this.countdown--;
            if (this.countdown <= 0) {
              clearInterval(interval);
              this.isCounting = false;
            }
          }, 1000);
        }
      })
    },
    nextStep() {
      if (this.activeStep === 0) {
        // 验证身份步骤
        const data = {
          userName: this.forgetForm.username,
          random: this.forgetForm.captcha,
          eventType: 1
        }
        confirmPassword(data).then(res => {
          console.log(res, '-------');
          if (res.code === 200) {
            this.userId = res.data.userId
            this.$message.success('验证成功')
            this.activeStep = 1
          }
        })
      } else if (this.activeStep === 1) {
        // 重置密码步骤
        this.$refs.resetForm.validate(valid => {
          if (valid) {
            const data = {
              newPassword: encrypt(this.resetForm.newPassword),
              confirmPassword: encrypt(this.resetForm.confirmPassword),
              userId: this.userId,
              type: '0'
            }
            resetPassword(data).then(res => {
              console.log(res, '-------');
              if (res.code === 200) {
                this.$message.success('密码重置成功')
                this.activeStep = 2
              }
            })
          } else {
            return false;
          }
        });
      } else if (this.activeStep === 2) {
        // 完成步骤
        this.forgetPassDialogVisible = false
      }
    }
  }
}
</script>

<style scoped lang="scss">
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: url('@/assets/images/bg.png') no-repeat center center fixed; // 修改背景为图片
  background-size: cover; // 确保背景图片覆盖整个容器
}
/deep/ .el-tabs__nav-wrap::after{
  width: 0;
}

.login-card {
  display: flex;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  width: 800px;
}

.left-section {
  width: 450px;
  border-radius: 8px 0 0 8px;
  background-color: #F5FAFF;
  padding: 30px 20px 20px;
  display: flex;
  align-items: end;
  .title {
    font-size: 24px;
    margin-bottom: 20px;
  }
  .description {
    font-size: 16px;
    color: #102642;
    margin-bottom: 40px;
    text-align: justify;
    line-height: 30px;
  }
}

.right-section {
  padding: 30px 20px;
  width: 350px;
  .welcome-title {
    font-size: 20px;
    margin-bottom: 10px;
  }
  .login-code{
    border: solid 1px #dddddd;
    border-radius: 4px;
    height: 40px;
    padding: 0 !important;
  }
  .login-btn {
    width: 100%;
  }
  .forgot-password {
    float: right;
    color: #007bff;
    text-decoration: underline;
  }
  .browser-recommend {
    font-size: 12px;
    color: #999;
    margin-top: 20px;
  }
  .qr-login-container{
    text-align: center;
    margin-bottom: 30px;
    .qr-code-container{
      width: 200px;
      height: 200px;
      margin: 10px auto 20px;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      .qr-code-wrapper {
        position: relative;
        width: 200px;
        height: 200px;
        .qr-code-img {
          width: 100%;
          height: 100%;
        }
        .expired-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5); // 透明遮罩
          color: #fff;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          p {
            font-size: 14px;
            line-height: 1.5;
          }
        }
      }
    }
    .expired {
      .qr-code-img {
        opacity: 0.5; // 过期时二维码半透明
      }
    }
    .qr-code-tips{
      color: #FFFFFF;
      font-weight: 500;
      font-size: 12px;
    }
    .qr-code{
      color: #17233D;
      font-weight: 500;
      font-size: 14px;
      margin-bottom: 10px;
    }
    .qr-code2{
      color: #515A6E;
      font-weight: 400;
      font-size: 13px;
    }
  }
}
/deep/.el-tabs__nav-scroll{
  display: flex;
  align-items: center;
  justify-content: center;
}
/deep/.el-form-item__label{
  line-height: normal;
}
/deep/.el-form-item{
  margin-bottom: 18px;
}
/deep/.el-row{
  margin-right: 0 !important;
}
/deep/.el-steps {
  margin:10px 0 20px;
}
.forget-form{
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 40px;
  // /deep/.el-form-item__label{
  //   margin-bottom: 10px;
  // }
}
.success-message{
 text-align: center;
}
.success-message-text{
  color: #027A48;
  font-weight: 700;
  font-size: 24px;
  margin: 20px 0 10px;
}
.success-message-tips{
  color: #515A6E;
  font-weight: 400;
  font-size: 16px;
  margin-bottom: 30px;
}
.success-message-tips2{
  color: #808695;
  font-weight: 400;
  font-size: 14px;
}
/deep/.el-step__title.is-process{
  color: #3566F4;
}
.is-process {
  /deep/.el-step__icon-inner{
    color: #FFFFFF;
  }
  /deep/.el-step__icon.is-text{
    border-color: #3566F4;
    background-color: #3566F4;
  }
}
</style>