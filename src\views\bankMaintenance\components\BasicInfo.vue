<template>
    <div class="basic-info">
      <el-form label-position="top" :model="editableData" :rules="rules" ref="editableData">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="info-item">
              <div class="label"><span v-if="isEdit" class="required-star">*</span>银行名称：</div>
              <el-form-item prop="companyName">
                <div v-if="!isEdit" class="value">{{ bankInfo.companyName  || "/" }}</div>
                <div v-else class="value">
                    <el-input v-model.trim="editableData.companyName" placeholder="请输入银行名称"></el-input> 
                </div>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="label"><span v-if="isEdit" class="required-star">*</span>金融机构编码：</div>
              <el-form-item prop="financialInstitutionCode">
                <div v-if="!isEdit" class="value">{{ bankInfo.financialInstitutionCode || "/" }}</div>
                <div v-else class="value">
                    <el-input v-model.trim="editableData.financialInstitutionCode" placeholder="请输入金融机构编码"></el-input>
                </div>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="label"><span v-if="isEdit" class="required-star">*</span>银行类型：</div>
              <el-form-item prop="companyType">
                <div v-if="!isEdit" class="value">{{ bankInfo.companyTypeName || "/" }}</div>
                <div v-else class="value">
                    <el-select v-model="editableData.companyType" style="width: 100%;" placeholder="请选择银行类型">
                        <el-option
                        v-for="item in bankTypeList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                        </el-option>
                    </el-select>
                </div>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="label">所在地区：</div>
              <el-form-item prop="location">
                <div v-if="!isEdit" class="value">{{ bankInfo.location || "/" }}</div>
                <div v-else class="value">
                    <el-input v-model.trim="editableData.location" placeholder="请输入所在地区"></el-input>
                </div>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="info-item">
              <div class="label">成立日期：</div>
              <el-form-item prop="establishmentDate">
                <div v-if="!isEdit" class="value">{{ showDate(bankInfo.establishmentDate) || "/" }}</div>
                <div v-else class="value">
                    <el-date-picker
                        v-model="editableData.establishmentDate "
                        type="date"
                        style="width: 100%;"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                        placeholder="选择日期">
                    </el-date-picker>
                </div>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="label">营业状态：</div>
              <el-form-item prop="operatingStatus">
                <div v-if="!isEdit" class="value">{{ bankInfo.operatingStatus || "/" }}</div>
                <div v-else class="value">
                    <el-input v-model.trim="editableData.operatingStatus" placeholder="请输入营业状态"></el-input>
                </div>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="label">金融许可证号：</div>
              <el-form-item prop="financialLicenseNumber">
                <div v-if="!isEdit" class="value">{{ bankInfo.financialLicenseNumber || "/" }}</div>
                <div v-else class="value">
                    <el-input v-model.trim="editableData.financialLicenseNumber" placeholder="请输入金融许可证号"></el-input>
                </div>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="label">营业执照号：</div>
              <el-form-item prop="businessLicenseNumber">
                <div v-if="!isEdit" class="value">{{ bankInfo.businessLicenseNumber || "/" }}</div>
                <div v-else class="value">
                    <el-input v-model.trim="editableData.businessLicenseNumber" placeholder="请输入营业执照号"></el-input>
                </div>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </template>
  
  <script>
  import { dictBankType } from '@/apis/enum'
  import moment from 'moment';
  export default {
    props: {
      bankInfo: {
        type: Object,
        required: true,
      },
      basicInfoFormData: {
        type: Object,
        required: true,
        },
      isEdit: {
        type: Boolean,
        default: false,
      },
    },
    data() {
        return {
            editableData: {},
        bankTypeList: [],
          rules: {
            companyName: [
              { required: true, message: '请输入银行名称', trigger: 'blur' },
              { 
                pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{1,50}$/, 
                message: '仅支持中文、英文、数字，不超过50个字符', 
                trigger: 'blur' 
              }
            ],
            financialInstitutionCode: [
              { required: true, message: '请输入金融机构编码', trigger: 'blur' },
              {
                pattern: /^[A-Z0-9]{12}$/,
                message: '金融机构编码应为12位大写字母和数字组合',
                trigger: 'blur',
              }
            ],
            companyType: [
              { required: true, message: '请选择银行类型', trigger: 'change' }
            ],
          }
      };
    },
    watch: {
      basicInfoFormData: {
        handler(newVal) {
          this.editableData = { ...newVal };
        },
        deep: true,
        immediate: true
        },
    },
    async mounted() {
      this.editableData = { ...this.basicInfoFormData };
      const res = await dictBankType();
      this.bankTypeList = res.data.details
    },
    methods: {
      showDate(val) {
        return moment(val).format('YYYY-MM-DD');
      },
      getFormData() {
        return this.editableData;
      },
        handleSave() {
            this.$emit('save', this.editableData);
        },
    },
  };
  </script>
  
  <style scoped>
  .basic-info{
    margin-top: 10px;
  }
  .basic-info .info-item {
    margin-bottom: 30px;
  }
  
  .basic-info .label {
    color: #515A6E;
    font-weight: 400;
    font-size: 14px;
  }
  
  .basic-info .value {
    margin-top: 10px;
    color: #17233D;
    font-weight: 400;
    font-size: 14px;
  }
  .required-star{
    color: red;
    margin-right: 5px;
  }
  </style>