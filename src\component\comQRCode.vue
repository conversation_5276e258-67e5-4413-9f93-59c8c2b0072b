<template>
  <div class="qr-code">
    <canvas ref="qrCanvas"></canvas>
</div>
</template>
<script>
import QRCode from 'qrcode'; // 引入生成二维码插件
export default {
  name: 'QRCodes',
  props: {
    canvasWidth: {
      default: 180,
      type: Number
    },
    canvasHeight: {
      default: 180,
      type: Number
    },
    url: {
      default: '',
      type: String,
      required: true
    },
    logoUrl: {
      default: '',
      type: String,
      // required:true
    }
  },
  watch: {
    url(newUrl) {
      if (newUrl) {
        this.getQRCode(newUrl);
      }
    }
  },
  methods: {
    getQRCode() {
      let opts = {
        errorCorrectionLevel: 'H', // 容错级别,指二维码被遮挡可以扫出结果的区域比例
        type: 'image/png', // 生成的二维码类型
        quality: 0.3, // 二维码质量
        margin: 3, // 二维码留白边距
        width: this.canvasWidth, // 宽
        height: this.canvasHeight, // 高
        text: '1111', // 二维码内容
        color: {
          dark: '#0A4478', // 可选，前景色
          light: '#FFFFFF'// 背景色
        }
      };
      // 将获取到的数据（val）画到msg（canvas）上,加上时间戳动态生成二维码
      // console.log(this.url,'---this.url');
      // QRCode.toCanvas(this.$refs.qrCanvas, jsonStringForQRCode, error => {
      //   if (error) console.error(error)
      // })
      QRCode.toCanvas(this.$refs.qrCanvas, this.url, opts, function (error) {
        if (error) {
          console.log('加载失败！');
        }
      });
    },
  },
  async mounted() {
    await this.getQRCode()

    // 设置logo图标
    if (this.logoUrl != '') {
      let myCanvas = this.$refs.qrCanvas
      let ctx = myCanvas.getContext('2d')
      // 在Canvas画布 添加图片
      let img = new Image()
      img.crossOrigin = 'Anonymous';// 解决Canvas.toDataURL 图片跨域问题
      img.src = this.logoUrl;
      img.onload = () => {
        // 第一个设置的元素，第二三是位置，后面两个是宽和高
        // 居中的位置计算为 （二维码宽度-img宽度）/2
        let codeWidth = (this.canvasWidth * 0.75) / 2
        let codeHeight = (this.canvasHeight * 0.75) / 2
        ctx.drawImage(img, codeWidth, codeHeight, this.canvasWidth * 0.25, this.canvasHeight * 0.25)
      }
    }
  },
}
</script>

<style scoped>
  .qr-code{
    width:fit-content;
    width:-moz-fit-content;
    position: relative;
    /* border: 1px solid #EDEDED; */
    text-align: center;
    padding: 10px;
    margin-bottom: 0 !important;
    background-color: #FFFFFF;
  }
  .qr-code:hover>div{
    z-index: 0;
  }
  .tips{
   color: #666666;
    font-weight: 400;
    font-size: 12px;
    margin-top: -10px;
  }
</style>
