<template>
  <div class="tabs-view-container">
    <el-tabs
      v-model="activeTab"
      type="card"
      closable
      @tab-click="handleTabClick"
      @tab-remove="handleTabRemove"
    >
      <el-tab-pane
        v-for="item in visitedViews"
        :key="item.path"
        :label="item.title"
        :name="item.name"
      />
    </el-tabs>
  </div>
</template>

<script>
export default {
  name: 'TabsView',
  data() {
    return {
      activeTab: '',
      // 模拟已访问的视图
      visitedViews: []
    }
  },
  watch: {
    $route: {
      handler(route) {
        this.addView(route)
        this.activeTab = route.name
      },
      immediate: true
    }
  },
  methods: {
    addView(route) {
      if (!route.meta || !route.meta.title) return

      const { path, meta, name } = route
      // 检查是否已存在
      if (this.visitedViews.some((v) => v.name === name)) return

      this.visitedViews.push({
        name: route.name,
        path,
        title: meta.title || 'no-name'
      })
    },
    handleTabClick(tab) {
      this.$router.push({ name: tab.name })
    },
    handleTabRemove(targrtName) {
      // 不能关闭首页
      if (targrtName === 'Dashboard') {
        return
      }

      // 找到要关闭的标签索引
      const index = this.visitedViews.findIndex((item) => item.name === targrtName)

      // 删除标签
      if (index !== -1) {
        this.visitedViews.splice(index, 1)
      }

      // 如果关闭的是当前激活的标签，则需要跳转到其他标签
      if (this.activeTab === targrtName) {
        // 如果还有其他标签，则跳转到最后一个标签
        if (this.visitedViews.length) {
          this.activeTab = this.visitedViews[this.visitedViews.length - 1].path
          this.$router.push({ name: this.activeTab })
        } else {
          // 如果没有其他标签，则跳转到首页
          this.$router.push('/dashboard')
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.tabs-view-container {
  height: 40px;
  background: #fff;
  border-bottom: 1px solid #d8dce5;
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.12),
    0 0 3px 0 rgba(0, 0, 0, 0.04);

  .el-tabs {
    height: 40px;

    ::v-deep .el-tabs__header {
      margin: 0;
    }

    ::v-deep .el-tabs__nav {
      border: none;
    }

    ::v-deep .el-tabs__item {
      height: 40px;
      line-height: 40px;
      border: none !important;
      color: #495060;
      background: #fff;

      &.is-active {
        color: #409eff;
        background-color: #f5f7fa;
      }
    }
  }
}
</style>

