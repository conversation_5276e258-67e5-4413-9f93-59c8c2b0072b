<template>
  <div>
    <el-breadcrumb class="app-breadcrumb" separator="/">
      <transition-group name="breadcrumb">
        <el-breadcrumb-item v-for="(item, index) in levelList" :key="item.path">
          <span
            v-if="item.redirect === 'noRedirect' || index === levelList.length - 1"
            class="no-redirect"
          >
            {{ item.meta.title }}
          </span>
          <a v-else @click.prevent="handleLink(item)">{{ item.meta.title }}</a>
        </el-breadcrumb-item>
      </transition-group>
    </el-breadcrumb>
  </div>
</template>

<script>
export default {
  name: 'Breadcrumb',
  data() {
    return {
      levelList: []
    }
  },
  watch: {
    $route() {
      this.getBreadcrumb()
    }
  },
  created() {
    this.getBreadcrumb()
  },
  methods: {
    getBreadcrumb() {
      // 面包屑仅显示有meta.title的路由
      let matched = this.$route.matched
        .filter((item) => item.meta && item.meta.title)
        .reduce((acc, cur) => {
          // 去重处理：避免重复的title
          if (!acc.some((v) => v.meta.title === cur.meta.title)) {
            acc.push(cur)
          }
          return acc
        }, [])

      // 如果是空数组，至少显示首页
      if (matched.length === 0) {
        this.levelList = [{ path: '/dashboard', meta: { title: '首页' } }]
        return
      }

      // 确保至少有一个面包屑项
      this.levelList =
        matched.length > 0 ? matched : [{ path: '/dashboard', meta: { title: '首页' } }]
    },
    handleLink(item) {
      const { redirect, path } = item
      // 如果有重定向，则不可点击
      if (redirect) {
        this.$router.push(redirect)
        return
      }
      this.$router.push(path)
    }
  }
}
</script>

<style lang="scss" scoped>
.app-breadcrumb {
  display: inline-block;
  font-size: 14px;
  line-height: 24px;
  margin-left: 8px;
  width: 100%;

  .el-breadcrumb__item {
    margin-right: 10px;
  }

  .no-redirect {
    color: #606266;
    cursor: text;
    font-weight: 600;
  }

  a {
    color: #409eff;
    font-weight: 600;
    cursor: pointer;

    &:hover {
      color: #66b1ff;
    }
  }
}

.breadcrumb-enter-active,
.breadcrumb-leave-active {
  transition: all 0.5s;
}

.breadcrumb-enter,
.breadcrumb-leave-active {
  opacity: 0;
  transform: translateX(20px);
}

.breadcrumb-move {
  transition: all 0.5s;
}

.breadcrumb-leave-active {
  position: absolute;
}
</style>

