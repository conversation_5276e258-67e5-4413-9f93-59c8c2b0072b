import Vue from 'vue'
import Vuex from 'vuex'
import createPersistedState from 'vuex-persistedstate'
import getters from './getters'
import { refreshToken } from '@/apis/auth'
import Cookies from 'js-cookie'

Vue.use(Vuex)

// https://webpack.js.org/guides/dependency-management/#requirecontext
const modulesFiles = require.context('./modules', true, /\.js$/)

// you do not need `import app from './modules/app'`
// it will auto require all vuex module from modules file
const modules = modulesFiles.keys().reduce((modules, modulePath) => {
  // set './app.js' => 'app'
  const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1')
  const value = modulesFiles(modulePath)
  modules[moduleName] = value.default
  return modules
}, {})

export default new Vuex.Store({
  plugins: [
    createPersistedState({
      key: 'ebdn-store', // 存储的键名
      paths: ['auth.user', 'auth.authType', 'auth.token',], // 只持久化 user 状态
      storage: window.localStorage, // 使用 localStorage 存储
    })
  ],
  getters,
  modules
})

let refreshTimer = null
const REFRESH_INTERVAL = 5 * 60 * 1000; 
function getNextRefreshTime() {
  const lastTime = localStorage.getItem('lastRefreshTime');
  return lastTime ? parseInt(lastTime, 10) + REFRESH_INTERVAL : Date.now() + REFRESH_INTERVAL;
}
function refreshTokenAction() {
  return async () => {
    try {
      const res = await refreshToken()
      const newToken = res.data.token
      localStorage.setItem('token', newToken)
      Cookies.set('AMS-SIMPLE-ADMIN-TRADE-TOKEN', res.data.access_token)
      Cookies.set('authType', res.data.userType)
      localStorage.setItem('lastRefreshTime', Date.now().toString())
    } catch (error) {
      console.error('刷新 token 失败:', error)
    }
  }
}
export function startRefreshTokenInterval() {
  clearInterval(refreshTimer) // 避免重复启动
  const task = refreshTokenAction()
  refreshTimer = setInterval(task, REFRESH_INTERVAL)
  const now = Date.now();
  const nextTime = getNextRefreshTime();
  if (now >= nextTime) {
    task(); // 手动触发一次
  }
}

export function stopRefreshTokenInterval() {
  console.log(22222);
  clearInterval(refreshTimer)
}
