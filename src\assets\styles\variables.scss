/* 
 * 变量文件
 * 定义全局使用的颜色、字体、尺寸等变量
 */

/* 颜色系统 */
// 主题色
$primary-color: #3566f4;

// 辅助色
$secondary-color: #6c757d;
$success-color: #28a745;
$danger-color: #e74c3c;
$warning-color: #f39c12;
$info-color: #3498db;

// 中性色
$white: #ffffff;
$gray-100: #f8f9fa;
$gray-200: #e9ecef;
$gray-300: #dee2e6;
$gray-400: #ced4da;
$gray-500: #adb5bd;
$gray-600: #6c757d;
$gray-700: #495057;
$gray-800: #343a40;
$gray-900: #212529;
$black: #000000;

// 文本颜色
$text-primary: $gray-900;
$text-secondary: $gray-600;
$text-muted: $gray-500;
$text-light: $gray-100;

// 背景颜色
$bg-body: #f5f5f5;
$bg-blue: #1d4474;
$bg-component: $white;
$bg-hover: $gray-100;
$bg-active: $gray-200;

// 边框颜色
$border-color: $gray-300;
$border-color-light: $gray-200;
$border-color-dark: $gray-400;

/* 字体系统 */
// 字体族
$font-family-base: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
$font-family-code: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;

// 字体大小
$font-size-base: 14px;
$font-size-xs: 12px;
$font-size-sm: 13px;
$font-size-md: $font-size-base;
$font-size-lg: 16px;
$font-size-xl: 18px;
$font-size-xxl: 20px;

// 字体粗细
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-bold: 700;

// 行高
$line-height-tight: 1.25;
$line-height-base: 1.5;
$line-height-loose: 1.75;

// 边框
$border-width: 1px;
$border-radius-sm: 2px;
$border-radius: 4px;
$border-radius-lg: 8px;
$border-radius-xl: 12px;
$border-radius-circle: 50%;

// 阴影
$box-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
$box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
$box-shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.1);

/* 动画 */
$transition-base: all 0.3s ease;
$transition-fast: all 0.15s ease;
$transition-slow: all 0.5s ease;

/* 布局 */
$container-max-width: 1200px;
$container-padding: 0 15px;

/* Z-index 层级 */
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

