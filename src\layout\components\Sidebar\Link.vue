<template>
  <!-- 外部链接 -->
  <a v-if="isExternalLink(to)" :href="to" target="_blank" rel="noopener">
    <slot />
  </a>
  <!-- 内部链接 -->
  <router-link v-else :to="to">
    <slot />
  </router-link>
</template>

<script>
export default {
  name: 'AppLink',
  props: {
    to: {
      type: String,
      required: true
    }
  },
  methods: {
    isExternalLink(path) {
      return /^(https?:|mailto:|tel:)/.test(path)
    }
  }
}
</script>
