<template>
  <div>
    <h3>离岸贸易业务印花税减免申报</h3>
    <div class="trade-list-container">
      <!-- 搜索表单 -->

      <el-form :inline="true" :model="searchForm" label-position="top" class="search-form">
        <el-form-item label="业务状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option
              v-for="item in tradeStatusParam"
              :key="item.detailId"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="申报日期">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="~"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="合同编号">
          <el-input
            v-model="searchForm.contractNo"
            placeholder="请输入合同编号"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="提单号">
          <el-input v-model="searchForm.eblNo" placeholder="请输入提单号" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <div class="add-btn">
        <el-button type="primary" size="mini" @click="handleCreate">新增</el-button>
      </div>
      <el-table key="stamp-table" :data="tableData" class="table_Header" border style="width: 100%">
        <el-table-column type="index" width="50" label="序号"> </el-table-column>
        <el-table-column prop="reportNo" label="业务编号" width="120"></el-table-column>
        <el-table-column prop="contactNos" label="合同编号" width="260"></el-table-column>
        <el-table-column prop="eblNos" label="提单号" width="180"></el-table-column>
        <el-table-column prop="declareDateStr" label="申报日期" width="180"></el-table-column>
        <el-table-column
          prop="bankInfo.companyName"
          label="金融机构名称"
          width="180"
        ></el-table-column>
        <el-table-column
          prop="bankInfo.financialInstitutionCode"
          label="金融机构代码"
          width="180"
        ></el-table-column>
        <el-table-column prop="businessStatus" label="业务状态" width="140">
          <template slot-scope="scope">
            <span class="status-label" :class="getStatusType(scope.row.businessStatus)"
              >{{ getStatusLabel(scope.row.businessStatus) }}
              <el-tooltip
                v-if="scope.row.businessStatus === 'cancel'"
                class="item"
                effect="dark"
                content="业务未通过审核，请重新申报"
                placement="top"
              >
                <img src="@/assets/images/danger-info.png" class="info-img" alt="" />
              </el-tooltip>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <img
              v-if="['draft'].includes(scope.row.businessStatus)"
              class="action-img"
              src="@/assets/images/edit.png"
              alt=""
              @click="handleEdit(scope.row)"
            />
            <img
              v-if="scope.row.businessStatus === 'submit'"
              class="action-img"
              src="@/assets/images/view.png"
              alt=""
              @click="handleView(scope.row)"
            />
            <img
              v-if="['cancel'].includes(scope.row.businessStatus)"
              class="action-img"
              src="@/assets/images/refresh-submit.png"
              alt=""
              @click="handleEdit(scope.row)"
            />
            <img
              v-if="['draft', 'cancel'].includes(scope.row.businessStatus)"
              class="action-img"
              src="@/assets/images/delete.png"
              alt=""
              @click="handleDelete(scope.row)"
            />
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="pagination.pageNum"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getTradeListDataApi } from '@/apis/index'
export default {
  name: 'StampDeclaration',
  data() {
    return {
      searchForm: {
        contractNo: '',
        status: '',
        eblNo: '',
        dateRange: ''
      },
      loading: false,
      tableData: [],
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
    }
  },
  computed: {
    ...mapGetters(['tradeStatusParam'])
  },
  mounted() {
    this.handleSearch()
    this.$dispatch('enum/getTradeStatusParam')
  },
  methods: {
    initData() {},
    handleSearch() {
      const param = {
        pageNum: this.pagination.pageNum,
        pageSize: this.pagination.pageSize,
        contractNo: this.searchForm.contractNo,
        status: this.searchForm.status,
        eblNo: this.searchForm.eblNo,
        startDate: this.searchForm.dateRange?.[0] || '',
        endDate: this.searchForm.dateRange?.[1] || ''
      }
      getTradeListDataApi(param).then((res) => {
        if (res.code === 200) {
          this.tableData = res.data.records
          this.pagination.total = res.data.total
        }
      })
    },
    resetSearch() {
      this.searchForm = {
        contractNo: '',
        status: '',
        eblNo: '',
        dateRange: ''
      }
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.handleSearch()
    },
    handleCurrentChange(val) {
      this.pagination.pageNum = val
      this.handleSearch()
    },
    handleCreate() {
      console.log('新增业务')
      // 跳转到新增页
      this.$router.push({
        name: 'stampDeclarationDetail',
        params: { id: 'new' },
        query: { mode: 'create' }
      })
    },
    handleView(row) {
      console.log('查看业务', row.id)
      // 跳转到详情页
      this.$router.push({
        name: 'stampDeclarationDetail',
        params: { id: row.id },
        query: { mode: 'view' }
      })
    },
    handleEdit(row) {
      console.log('编辑业务', row.id)
      // 跳转到编辑页
      this.$router.push({
        name: 'stampDeclarationDetail',
        params: { id: row.id },
        query: { mode: 'edit' }
      })
    },
    handleDelete(row) {
      console.log('删除业务', row.id)
      this.$confirm('确认删除该业务记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 实际项目中这里应该调用删除API
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    getStatusType(status) {
      switch (status) {
        case 'draft':
          return 'draft'
        case 'submit':
          return 'submitted'
        case 'cancel':
          return 'cancel'
        default:
          return 'info'
      }
    },
    getStatusLabel(status) {
      switch (status) {
        case 'draft':
          return '草稿'
        case 'submit':
          return '已提交'
        case 'cancel':
          return '不通过'
        default:
          return status
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.trade-list-container {
  padding: 20px;
  background-color: #fff;
  margin-top: 20px;
}
.search-form {
  margin-bottom: 20px;
  display: flex;
  align-items: flex-end;
  .el-form-item {
    margin-right: 20px;
  }
}
.add-btn {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  background-color: #fafbfc;
  border: 1px solid #ededed;
  border-bottom: none;
  padding-right: 20px;
  height: 48px;
}
.status-label {
  display: flex;
  align-items: center;
  &::before {
    content: '';
    display: inline-block;
    box-sizing: border-box;
    width: 10px;
    height: 10px;
    margin-right: 10px;
    border-radius: 50%;
    border: 2px solid rgba($color: #fff, $alpha: 0.8);
  }
}
.draft {
  color: #3566f4;
  &::before {
    background-color: #3566f4;
  }
}
.submitted {
  color: #027a48;
  &::before {
    background-color: #027a48;
  }
}
.cancel {
  color: #515a6e;
  &::before {
    background-color: #515a6e;
  }
}
.info-img {
  width: 16px;
  height: 16px;
  margin-left: 15px;
}
.action-img {
  width: 24px;
  height: 24px;
  margin-right: 10px;
}
.pagination-container {
  margin-top: 20px;
}
</style>

