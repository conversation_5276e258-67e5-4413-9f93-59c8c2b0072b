{"name": "offShoreTrade-ui", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "lint:fix": "vue-cli-service lint --fix", "lint:style": "vue-cli-service lint --fix --rule 'indent: [\"error\", 2]' --rule 'quotes: [\"error\", \"single\"]'", "lint:report": "vue-cli-service lint --format html --output-file ./lint-report.html"}, "dependencies": {"axios": "^0.21.4", "core-js": "^3.8.3", "element-ui": "^2.15.14", "jsencrypt": "^3.3.2", "path-browserify": "^1.0.1", "qrcode": "^1.5.4", "vue": "^2.6.14", "vue-router": "^3.5.3", "vuex": "^3.6.2", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "^5.0.8", "@vue/cli-plugin-vuex": "^5.0.8", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-vue": "^7.20.0", "js-cookie": "^2.2.0", "moment": "^2.30.1", "node-sass": "^4.14.1", "prettier": "^3.5.3", "sass-loader": "^7.0.1", "vue-cropper": "^0.6.5", "vue-esign": "^1.1.4"}, "eslintConfig": {"root": true, "env": {"node": true, "browser": true, "es6": true}, "extends": ["plugin:vue/recommended", "eslint:recommended", "prettier"], "plugins": ["prettier"], "parserOptions": {"parser": "@babel/eslint-parser", "ecmaVersion": 2020, "sourceType": "module"}, "overrides": [{"files": ["*.vue"], "rules": {"indent": "off"}}]}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}