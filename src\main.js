import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

import './utils/request'
// 导入全局样式
import './assets/styles/main.scss'

import {
  Button,
  Form,
  FormItem,
  Input,
  Checkbox,
  Message,
  Table,
  TableColumn,
  Pagination,
  Select,
  Option,
  Row,
  Col,
  DatePicker,
  TimePicker,
  Tooltip,
  Dialog,
  Radio,
  Card,
  RadioGroup,
  Rate,
  CheckboxGroup,
  Menu,
  Submenu,
  MenuItem,
  MenuItemGroup,
  Dropdown,
  DropdownMenu,
  DropdownItem,
  Breadcrumb,
  BreadcrumbItem,
  Tag,
  Tabs,
  TabPane,
  Scrollbar,
  Steps,
  Step,
  Switch,
  MessageBox,
  Upload,
  Autocomplete,
  Image
} from 'element-ui'

Vue.use(Button)
Vue.use(Form)
Vue.use(FormItem)
Vue.use(Input)
Vue.use(Checkbox)
Vue.use(Table)
Vue.use(TableColumn)
Vue.use(Pagination)
Vue.use(Select)
Vue.use(Option)
Vue.use(Row)
Vue.use(Col)
Vue.use(DatePicker)
Vue.use(TimePicker)
Vue.use(Tooltip)
Vue.use(Dialog)
Vue.use(Radio)
Vue.use(Card)
Vue.use(RadioGroup)
Vue.use(Rate)
Vue.use(CheckboxGroup)
Vue.use(Menu)
Vue.use(Submenu)
Vue.use(MenuItem)
Vue.use(MenuItemGroup)
Vue.use(Dropdown)
Vue.use(DropdownMenu)
Vue.use(DropdownItem)
Vue.use(Breadcrumb)
Vue.use(BreadcrumbItem)
Vue.use(Tag)
Vue.use(Tabs)
Vue.use(TabPane)
Vue.use(Scrollbar)
Vue.use(Switch)
Vue.use(Steps)
Vue.use(Step)
Vue.use(Upload)
Vue.use(Autocomplete)
Vue.use(Image)

Vue.prototype.$message = Message
Vue.prototype.$confirm = MessageBox.confirm
Vue.prototype.$dispatch = store.dispatch

new Vue({
  router,
  store,
  render: (h) => h(App)
}).$mount('#app')

import { startRefreshTokenInterval } from '@/store'

const token = localStorage.getItem('token')
if (token) {
  startRefreshTokenInterval()
}
