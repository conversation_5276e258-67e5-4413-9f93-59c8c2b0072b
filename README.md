# EBDN 前端项目

基于 Vue 2 + Vue Router + Vuex + Axios 的前端项目。

## 仓库信息

- 仓库地址：https://codeup.aliyun.com/6333f9d44aae4ea056170bb9/slyl-wishare/web/ebdn-ui.git
- 分支：main

## 项目设置

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run serve

# 构建生产版本
npm run build

# 代码检查
npm run lint

# 自动修复代码问题
npm run lint:fix
```

## 环境变量

项目使用不同的环境变量文件来配置不同环境：

- `.env` - 所有环境的默认环境变量
- `.env.development` - 开发环境变量
- `.env.production` - 生产环境变量

详细信息请参考 [ENV_README.md](./ENV_README.md)。

## ESLint 配置

项目使用 ESLint 进行代码质量检查，配置了以下规则：

- Vue 推荐规则
- ESLint 推荐规则
- Prettier 集成

详细信息请参考 [ESLINT_README.md](./ESLINT_README.md)。

## 项目结构

```
ebdn/
├── public/             # 静态资源
├── src/
│   ├── assets/         # 项目资源
│   ├── components/     # 组件
│   ├── router/         # 路由配置
│   ├── services/       # API 服务
│   ├── store/          # Vuex 状态管理
│   ├── utils/          # 工具函数
│   ├── views/          # 页面视图
│   ├── App.vue         # 根组件
│   └── main.js         # 入口文件
├── .env                # 环境变量
├── .eslintrc           # ESLint 配置
└── package.json        # 项目配置
```
