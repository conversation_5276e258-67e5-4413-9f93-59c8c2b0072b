import Vue from 'vue'
import VueRouter from 'vue-router'
import Cookies from 'js-cookie'
import store from '@/store'
import { canAccessPath, getDefaultHomePath } from '@/utils/permission'
const Login = () => import('../views/Login/Login.vue')
const NotFound = () => import('../views/404/NotFound.vue')
const Layout = () => import('../layout/index.vue')

Vue.use(VueRouter)

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: '登录',
      requiresAuth: false
    }
  },
  {
    path: '/enterpriseInfoMaintenance',
    component: Layout, // 使用 Layout 作为父组件
    meta: {
      title: '企业信息维护',
      requiresAuth: false,
      roles: ['101'] // 管理员和企业用户可访问
    },
    children: [
      {
        path: '', // 空路径表示默认子路由
        name: 'EnterpriseInfoMaintenance',
        component: () => import('../views/enterpriseInfoMaintenance/index.vue'),
        meta: {
          title: '企业信息维护',
          requiresAuth: false
        }
      },
      {
        path: 'detail', // 空路径表示默认子路由
        name: 'companyDetail',
        component: () => import('../views/enterpriseInfoMaintenance/detail.vue'),
        meta: {
          title: '企业信息维护',
          requiresAuth: false,
          activeMenu: '/enterpriseInfoMaintenance'
        }
      }
    ]
  },
  {
    path: '/bankMaintenance',
    component: Layout, // 使用 Layout 作为父组件
    meta: {
      title: '商业银行维护',
      requiresAuth: false,
      roles: ['101'] // 管理员和银行用户可访问
    },
    children: [
      {
        path: '',
        name: 'bankMaintenance',
        component: () => import('../views/bankMaintenance/index.vue'),
        meta: {
          title: '商业银行维护',
          requiresAuth: false
        }
      },
      {
        path: 'detail',
        name: 'bankDetail',
        component: () => import('../views/bankMaintenance/detail.vue'),
        meta: {
          title: '商业银行维护',
          requiresAuth: false,
          activeMenu: '/bankMaintenance'
        }
      }
    ]
  },
  {
    path: '/stamp-declaration',
    component: Layout,
    redirect: '/stamp-declaration/list',
    meta: {
      title: '印花税减免申报',
      requiresAuth: true,
      roles: ['102'] // 只有企业用户可访问
    },
    children: [
      {
        path: 'list',
        name: 'StampDeclaration',
        component: () => import('../views/stampDeclaration/list.vue'),
        meta: {
          title: '印花税减免申报',
          requiresAuth: true
        }
      },
      {
        path: 'detail/:id',
        name: 'stampDeclarationDetail',
        component: () => import('../views/stampDeclaration/detail.vue'),
        meta: {
          title: '业务详情',
          requiresAuth: true,
          activeMenu: '/stamp-declaration/list'
        }
      }
    ]
  },
  {
    path: '/trade-verification',
    component: Layout,
    redirect: '/trade-verification/list',
    meta: {
      title: '离岸贸易业务核验',
      requiresAuth: true,
      roles: ['104'] // 只有银行用户可访问
    },
    children: [
      {
        path: 'list',
        name: 'TradeVerification',
        component: () => import('../views/tradeVerification/list.vue'),
        meta: {
          title: '离岸贸易业务核验',
          requiresAuth: true
        }
      },
      {
        path: 'detail/:id',
        name: 'TradeVerificationDetail',
        component: () => import('../views/tradeVerification/detail.vue'),
        meta: {
          title: '业务详情',
          requiresAuth: true,
          activeMenu: '/trade-verification/list'
        }
      }
    ]
  },
  {
    path: '/infomation-review',
    component: Layout,
    redirect: '/infomation-review/list',
    meta: {
      title: '申报信息审核',
      requiresAuth: true,
      roles: ['108'] // 运维人员
    },
    children: [
      {
        path: 'list',
        name: 'InfomationReview',
        component: () => import('../views/infomationReview/list.vue'),
        meta: {
          title: '申报信息审核',
          requiresAuth: true
        }
      },
      {
        path: 'detail/:id',
        name: 'InfomationReviewDetail',
        component: () => import('../views/infomationReview/detail.vue'),
        meta: {
          title: '业务详情',
          requiresAuth: true,
          activeMenu: '/infomation-review/list'
        }
      }
    ]
  },
  {
    path: '/infomation-approval',
    component: Layout,
    redirect: '/infomation-approval/list',
    meta: {
      title: '申报信息批复',
      requiresAuth: true,
      roles: ['105', '106'] // 自贸区和商务局
    },
    children: [
      {
        path: 'list',
        name: 'InfomationApproval',
        component: () => import('../views/infomationApproval/list.vue'),
        meta: {
          title: '申报信息批复',
          requiresAuth: true
        }
      },
      {
        path: 'detail/:id',
        name: 'InfomationApprovalDetail',
        component: () => import('../views/infomationApproval/detail.vue'),
        meta: {
          title: '业务详情',
          requiresAuth: true,
          activeMenu: '/infomation-approval/list'
        }
      }
    ]
  },
  {
    path: '/account-management',
    component: Layout,
    redirect: '/account-management/list',
    meta: {
      title: '账号管理',
      requiresAuth: true,
      roles: ['101'] // 只有管理员可访问
    },
    children: [
      {
        path: 'list',
        name: 'AccountManagement',
        component: () => import('../views/accountManage/index.vue'),
        meta: {
          title: '账号列表',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/reconsideration-declaration',
    component: Layout, // 使用 Layout 作为父组件
    meta: {
      title: '核验报告',
      requiresAuth: true,
      roles: ['103', '105', '107'] // 税务局、自贸区、中国人民银行查看
    },
    children: [
      {
        path: 'list',
        name: 'ReconsiderationDeclaration',
        component: () => import('../views/reconsiderationDeclaration/index.vue'),
        meta: {
          title: '核验报告',
          requiresAuth: true
        }
      },
      {
        path: 'detail/:id',
        name: 'ReconsiderationDeclarationDetail',
        component: () => import('../views/reconsiderationDeclaration/detail.vue'),
        meta: {
          title: '核验报告详情',
          requiresAuth: true,
          activeMenu: '/reconsideration-declaration/list'
        }
      }
    ]
  },
  // 404页面
  {
    path: '*',
    name: 'NotFound',
    component: NotFound,
    meta: {
      requiresAuth: false
    }
  }
]

const router = new VueRouter({
  mode: 'hash',
  scrollBehavior(to) {
    // 始终滚动到页面顶部
    console.log('路由滚动行为触发:', to.path)
    document.body.scrollTop = 0
    return new Promise((resolve) => {
      setTimeout(() => {
        window.scrollTo({
          top: 0,
          left: 0,
          behavior: 'smooth'
        })
        resolve({ x: 0, y: 0 })
      }, 100)
    })
  },
  routes
})

// 全局前置守卫
router.beforeEach((to, _from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title
  }

  // 从 store 获取当前的用户信息
  const token = Cookies.get('AMS-SIMPLE-ADMIN-TRADE-TOKEN')
  const authType = Cookies.get('authType')

  if (token) {
    if (to.path === '/login') {
      // 已登录用户访问登录页，重定向到默认首页
      const defaultPath = getDefaultHomePath(store.getters.getUser, authType)
      next({ path: defaultPath })
    } else if (to.path === '/') {
      // 访问根路径，重定向到默认首页
      const defaultPath = getDefaultHomePath(store.getters.getUser, authType)
      next({ path: defaultPath })
    } else {
      // 检查用户是否有权限访问该路由
      if (to.meta.requiresAuth !== false && (to.meta.roles || to.meta.authTypes)) {
        const hasAccess = canAccessPath(to.path, routes, store.getters.getUser, authType)
        if (hasAccess) {
          next()
        } else {
          // 没有权限，重定向到默认首页
          const defaultPath = getDefaultHomePath(store.getters.getUser, authType)
          next({ path: defaultPath })
        }
      } else {
        next()
      }
    }
  } else {
    if (to.meta.requiresAuth) {
      next({ path: '/login' })
    } else {
      next()
    }
  }
})

export default router
