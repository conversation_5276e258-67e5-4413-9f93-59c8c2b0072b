<template>
  <div>
    <h3>企业信息维护</h3>
    <div class="enterprise-detail">
      <div class="enterprise-detail-title">
          <div @click="goBack" style="display: flex;align-items: center;"> <img v-if="!isAdd" :src="require('@/assets/images/back.png')" style="width: 24px;margin-right: 5px;" alt=""/> {{ enterpriseData.companyName }}</div>
          <el-button v-if="!isEdit" type="primary" @click="handleEdit" style="float: right;">
              编辑
          </el-button>
          <div v-if="isEdit" style="float: right; display: flex;">
            <el-button @click="handleClose" style="margin-right: 10px;">
                取消
              </el-button>
            <div v-if="!isAdd">
              <el-button type="primary" @click="handleSave" >
                保存
              </el-button>
            </div>
            <div v-else>
              <el-button v-if="this.activeName == 'financialInfo'" type="primary" @click="handleSave" >
                保存
              </el-button>
              <el-button v-else type="primary" @click="nextStep" >
                下一步
              </el-button>
            </div>
          </div>
      </div>
      <!-- 标签页 -->
      <el-tabs v-model="activeName" class="enterprise-detail-tabs">
        <el-tab-pane label="基本信息" name="basicInfo">
          <BasicInfo :key="'basic-info-' + isEdit" :enterpriseData="enterpriseData" :basicInfoFormData.sync="basicInfoFormData" :isEdit.sync="isEdit" ref="basicInfoForm" />
        </el-tab-pane>
        <el-tab-pane label="联系信息" name="contactInfo">
          <ContactInfo :key="'contact-info-' + isEdit" :enterpriseData="enterpriseData" :contactInfoFormData="contactInfoFormData" :isEdit="isEdit" ref="contactInfoForm" />
        </el-tab-pane>
        <el-tab-pane label="经营信息" name="businessInfo">
          <BusinessInfo :key="'business-info-' + isEdit" :enterpriseData="enterpriseData" :businessInfoFormData="businessInfoFormData" :isEdit="isEdit" ref="businessInfoForm" />
        </el-tab-pane>
        <el-tab-pane label="财务信息" name="financialInfo">
          <FinancialInfo :key="'financial-info-' + isEdit" :enterpriseData="enterpriseData" :financialInfoFormData="financialInfoFormData" :isEdit="isEdit" ref="financialInfoForm" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import BasicInfo from './components/BasicInfo.vue';
import ContactInfo from './components/ContactInfo.vue';
import BusinessInfo from './components/BusinessInfo.vue';
import FinancialInfo from './components/FinancialInfo.vue';
import { createCompanyApi,getCompanyDetailApi,updateCompanyDetailApi } from '@/apis/index';

export default {
  components: {
    BasicInfo,
    ContactInfo,
    BusinessInfo,
    FinancialInfo,
  },
  data() {
    return {
      activeName: 'basicInfo',
      isEdit: false,
      isAdd: false,
      basicInfoFormData: {
        companyName: '',
        creditCode: '',
        companyType: '',
        legalRepresentative: '',
        registeredCapital: '',
        actualCapital: '',
        registrationAuthority: '',
        establishmentDate: '',
        approvalDate: '',
        scopeItem: '',
        industry: '',
        operatingPeriod: '',
      },
      contactInfoFormData: {
        registrationAddress: '',
        officeAddress: '',
        contactPerson: '',
        contactPhone: '',
        email: '',
        postalCode: '',
      },
      businessInfoFormData: {
        taxpayerQualification :'',
        importExportCode :'',
        customsCode :'',
        openingBank :'',
        bankAccount :'',
        mainBusiness :'',
        annualRevenue :'',
        employeeCount :'',
      },
      financialInfoFormData: {
        taxRegistrationNumber :'',
        taxpayerIdentificationNumber :'',
        registeredCurrency :'',
        registeredCapitalCurrency :'',
        latestAuditReport :[],
        latestTaxProof :[],
      },
      enterpriseData: {
        companyName: '',
        creditCode: '',
        companyType: '',
        companyTypeName: '',
        legalRepresentative: '',
        registeredCapital: '',
        actualCapital: '',
        registrationAuthority: '',
        establishmentDate: '',
        approvalDate: '',
        scopeItem: '',
        industry: '',
        operatingPeriod: '',
        registrationAddress: '',
        officeAddress: '',
        contactPerson: '',
        contactPhone: '',
        email: '',
        postalCode: '',
        taxpayerQualification :'',
        importExportCode :'',
        customsCode :'',
        openingBank :'',
        bankAccount :'',
        mainBusiness :'',
        annualRevenue :'',
        employeeCount :'',
        taxRegistrationNumber :'',
        taxpayerIdentificationNumber :'',
        registeredCurrency :'',
        registeredCapitalCurrency :'',
        latestAuditReport :[],
        latestTaxProof :[],
      },
    };
  },
  async mounted() {
    if (this.$route.query.isEdit) {
      this.isEdit = true
    }
    if (this.$route.query.isAdd) {
      this.isAdd = true
      this.isEdit = true
    }
    if (this.$route.query.id) {
      this.getCompanyDetail();
    }
  },
  methods: {
    getCompanyDetail() {
      getCompanyDetailApi(this.$route.query.id).then(res => {
        if (res.code == 200) {
          this.enterpriseData = res.data;
          this.enterpriseData.operatingPeriod =[
            res.data.businessTermStart,
            res.data.businessTermEnd
          ];
          Object.keys(this.basicInfoFormData).forEach(key => {
            if (Object.prototype.hasOwnProperty.call(res.data, key)) {
              this.basicInfoFormData[key] = res.data[key];
            }
          });
          Object.keys(this.contactInfoFormData).forEach(key => {
            if (Object.prototype.hasOwnProperty.call(res.data, key)) {
              this.contactInfoFormData[key] = res.data[key];
            }
          });
          Object.keys(this.businessInfoFormData).forEach(key => {
            if (Object.prototype.hasOwnProperty.call(res.data, key)) {
              this.businessInfoFormData[key] = res.data[key];
            }
          });
          Object.keys(this.financialInfoFormData).forEach(key => {
            if (Object.prototype.hasOwnProperty.call(res.data, key)) {
              this.financialInfoFormData[key] = res.data[key];
            }
          });
        }
      });
    },
    handleEdit() {
      this.isEdit = true;
    },
    async validateCurrentTabForm() {
      const formRefs = {
        basicInfo: this.$refs.basicInfoForm,
        contactInfo: this.$refs.contactInfoForm,
        businessInfo: this.$refs.businessInfoForm,
        financialInfo: this.$refs.financialInfoForm
      };

      const currentFormRef = formRefs[this.activeName];

      if (currentFormRef && currentFormRef.$refs.editableData) {
        try {
          await currentFormRef.$refs.editableData.validate();
          return true;
        } catch (error) {
          this.$message.error('请检查当前表单内容');
          return false;
        }
      }
      return true;
    },
    async nextStep() {
      const isValid = await this.validateCurrentTabForm();
      if (isValid) {
        if (this.activeName === 'basicInfo') {
          this.activeName = 'contactInfo';
        } else if (this.activeName === 'contactInfo') {
          this.activeName = 'businessInfo';
        } else if (this.activeName === 'businessInfo') {
          this.activeName = 'financialInfo';
        }
      }
    },
    async handleSave() {
      const formRefs = [
        this.$refs.basicInfoForm,
        this.$refs.contactInfoForm,
        this.$refs.businessInfoForm,
        this.$refs.financialInfoForm
      ];
      const basicInfoFormData = this.$refs.basicInfoForm.getFormData();
      const contactInfoFormData = this.$refs.contactInfoForm.getFormData();
      const businessInfoFormData = this.$refs.businessInfoForm.getFormData();
      const financialInfoFormData = this.$refs.financialInfoForm.getFormData();
      let isValid = true;
      for (const ref of formRefs) {
        if (ref && ref.$refs.editableData) {
          try {
            await ref.$refs.editableData.validate();
          } catch (error) {
            isValid = false;
            break;
          }
        }
      }
      const formData = { ...basicInfoFormData, ...contactInfoFormData, ...businessInfoFormData, ...financialInfoFormData }
      formData.businessTermStart = basicInfoFormData.operatingPeriod[0]
      formData.businessTermEnd = basicInfoFormData.operatingPeriod[1]
      formData.organRoleList = [102]
      if (isValid) {
        console.log('所有表单校验通过，准备提交:',this.isAdd,formData);
        // 调用保存接口
        if (this.isAdd) {
          createCompanyApi(formData).then(res => {
            if (res.code === 200) {
              this.isEdit = false;
              this.$router.replace('/enterpriseInfoMaintenance')
            }
            return
          });
        }
        if (this.isEdit && !this.isAdd) {
          formData.id = this.$route.query.id
          updateCompanyDetailApi(formData).then(res => {
            console.log('提交成功:', res);
            this.isEdit = false;
            this.getCompanyDetail();
          });
        }
      } else {
        this.$message.error('请检查表单内容');
      }
    },
    goBack() {
      this.$router.replace('/enterpriseInfoMaintenance')
    },
    hasAnyFilled(formData) {
      return Object.values(formData).some(value => {
        if (typeof value === 'string') {
          return value.trim() !== '';
        }
        if (typeof value === 'number' && !isNaN(value)) {
          return true;
        }
        if (Array.isArray(value)) {
          return value.length > 0;
        }
        if (value instanceof Date) {
          return !isNaN(value.getTime());
        }
        return false;
      });
    },
    handleClose() {
      if (this.isAdd) {
        const basicInfoFormData = this.$refs.basicInfoForm.getFormData();
        const contactInfoFormData = this.$refs.contactInfoForm.getFormData();
        const businessInfoFormData = this.$refs.businessInfoForm.getFormData();
        const financialInfoFormData = this.$refs.financialInfoForm.getFormData();
        const hasBasic = this.hasAnyFilled(basicInfoFormData);
        const hasContact = this.hasAnyFilled(contactInfoFormData);
        const hasBusiness = this.hasAnyFilled(businessInfoFormData);
        const hasFinancial = this.hasAnyFilled(financialInfoFormData);
        const hasAnyData = hasBasic || hasContact || hasBusiness || hasFinancial;
        if (hasAnyData) {
          this.$confirm('当前有填写内容，是否确认退出新增？', '提示', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$router.replace('/enterpriseInfoMaintenance');
          }).catch(() => {});
        } else {
          this.$router.replace('/enterpriseInfoMaintenance');
        }
      }
      if (this.isEdit && !this.isAdd) {
        this.$confirm('是否确认放弃修改？', '提示', {
          confirmButtonText: '确认',
          type: 'warning'
          }).then(async () => {
            this.isEdit = false;
            this.resetFormValidation();
          }).catch(() => {
        });
      }
    },
    resetFormValidation() {
      const formRefs = ['basicInfoForm', 'contactInfoForm', 'businessInfoForm', 'financialInfoForm'];
      formRefs.forEach(ref => {
        if (this.$refs[ref]) {
          this.$refs[ref].$refs.editableData.resetFields();
        }
      });
    },
  },
};
</script>

<style scoped>
  .enterprise-detail {
    padding: 20px 20px 30px;
    border-radius: 8px;
    background-color: #FFFFFF;
    margin-top: 20px;
  }
  .enterprise-detail-title {
    color: #17233D;
    font-weight: 500;
    font-size: 24px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .enterprise-detail-tabs{
    padding: 10px 20px;
    border: 1px solid #EDEDED;
  }
  </style>