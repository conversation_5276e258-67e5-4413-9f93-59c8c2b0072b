<template>
  <div>
    <h3>离岸贸易业务印花税减免申报</h3>
    <div class="trade-detail-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <div class="back-btn" @click="goBack"><i class="el-icon-arrow-left"></i> 返回列表</div>
        <div class="footer-actions">
          <el-button size="small" @click="goBack">取消</el-button>
          <el-button v-if="!isViewMode" size="small" @click="handleSave('draft')">保存</el-button>
          <el-button v-if="!isViewMode" size="small" type="primary" @click="handleSave('submit')"
            >提交</el-button
          >
        </div>
      </div>

      <div class="info-card" v-if="detailData.businessStatus === 'cancel'">
        <span class="card-title">审批意见</span>
        <div>{{ detailData.auditRemark }}</div>
      </div>
      <!-- 业务申报基本信息 -->
      <div class="info-card">
        <span class="card-title">业务申报基本信息</span>

        <el-form
          ref="basicForm"
          :model="detailData"
          :rules="basicFormRules"
          label-width="120px"
          label-position="top"
          class="detail-form"
        >
          <el-row :gutter="24">
            <el-col :span="6">
              <el-form-item label="企业名称">
                <el-input
                  v-if="!isViewMode"
                  v-model="detailData.companyName"
                  disabled
                  placeholder="请输入企业名称"
                ></el-input>
                <span v-else class="form-value">{{ detailData?.companyName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="企业组织机构代码">
                <el-input
                  v-if="!isViewMode"
                  v-model="detailData.creditCode"
                  disabled
                  placeholder="请输入企业组织机构代码"
                ></el-input>
                <span v-else class="form-value">{{ detailData.creditCode }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="授权银行" prop="targetBankId">
                <el-select
                  v-if="!isViewMode"
                  v-model="detailData.targetBankId"
                  @change="handleSelectBank"
                >
                  <el-option
                    v-for="item in bankList"
                    :key="item.id"
                    :label="item.companyName"
                    :value="item.id"
                  ></el-option>
                </el-select>
                <span v-else class="form-value">{{
                  bankList?.find((item) => item.id === detailData.targetBankId)?.companyName
                }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="金融机构代码">
                <el-input
                  v-if="!isViewMode"
                  v-model="detailData.financialInstitutionCode"
                  disabled
                  placeholder="请输入"
                ></el-input>
                <span v-else class="form-value">{{ detailData.financialInstitutionCode }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 合同信息 -->
      <div class="info-card">
        <span class="card-title">合同信息</span>
        <div v-if="!isViewMode" class="add-btn">
          <el-button type="text" icon="el-icon-plus" @click="handleAdd('contractInfos')"
            >新增合同信息</el-button
          >
        </div>
        <el-table :data="contractInfos" border style="width: 100%" class="table_Header">
          <el-table-column label="合同类型" width="120">
            <template slot-scope="scope">
              <div class="table-form-item">
                <el-select
                  v-model="scope.row.contractType"
                  :disabled="isViewMode"
                  placeholder="请选择"
                  size="mini"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in contractTypeParam"
                    :key="item.detailId"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="合同编号" min-width="160">
            <template slot-scope="scope">
              <div class="table-form-item">
                <el-input
                  v-model="scope.row.contractNo"
                  :disabled="isViewMode"
                  placeholder="请输入"
                  size="mini"
                ></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="书立日期" width="200">
            <template slot-scope="scope">
              <el-date-picker
                :disabled="isViewMode"
                v-model="scope.row.contractDate"
                type="date"
                placeholder="请选择"
                size="mini"
                style="width: 100%"
                value-format="yyyy-MM-dd"
                :picker-options="pickerOptions"
              ></el-date-picker>
            </template>
          </el-table-column>

          <el-table-column label="购买方" width="160">
            <template slot-scope="scope">
              <div class="table-form-item">
                <el-input
                  v-model="scope.row.purchaser"
                  :disabled="isViewMode"
                  placeholder="请输入"
                  size="mini"
                ></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="销售方" width="160">
            <template slot-scope="scope">
              <div class="table-form-item">
                <el-input
                  v-model="scope.row.seller"
                  :disabled="isViewMode"
                  placeholder="请输入"
                  size="mini"
                ></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="申报金额(万)" width="160">
            <template slot-scope="scope">
              <div class="table-form-item">
                <el-input
                  v-model="scope.row.declaredAmount"
                  :disabled="isViewMode"
                  placeholder="请输入"
                  size="mini"
                ></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="减免税额(万)" width="160">
            <template slot-scope="scope">
              <div class="table-form-item">
                <el-input
                  v-model="scope.row.taxSavingAmount"
                  :disabled="isViewMode"
                  placeholder="请输入"
                  size="mini"
                ></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="合同币种" width="160">
            <template slot-scope="scope">
              <div class="table-form-item">
                <el-select
                  v-model="scope.row.currencyType"
                  :disabled="isViewMode"
                  placeholder="请选择"
                  size="mini"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in currencyTypeParam"
                    :key="item.detailId"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="合同金额(万)" width="160">
            <template slot-scope="scope">
              <div class="table-form-item">
                <el-input
                  v-model="scope.row.amount"
                  :disabled="isViewMode"
                  placeholder="请输入"
                  size="mini"
                  type="number"
                ></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="合同附件" width="180">
            <template slot-scope="scope">
              <input
                :ref="'contractFile' + scope.$index"
                accept=".pdf,.jpg,.png,.jpeg"
                type="file"
                style="display: none"
                @change="(e) => uploadContractFile(scope, 'contractInfos', e)"
              />
              <el-button
                v-if="!isViewMode && !scope.row.attachments?.[0]?.id"
                size="mini"
                type="text"
                @click="uploadContract(scope, 'contractFile')"
              >
                上传
              </el-button>
              <div v-else class="table-cell-value">
                <BaseUpload
                  :businessOtherFiles="scope.row.attachments"
                  :isViewMode="isViewMode"
                  needDelete
                  @deleteAttachment="deleteAttachment(scope, 'contractInfos')"
                ></BaseUpload>
              </div>
            </template>
          </el-table-column>
          <el-table-column v-if="!isViewMode" label="操作" width="80">
            <template slot-scope="scope">
              <img
                v-if="scope.$index === 0"
                class="action-img"
                src="@/assets/images/delete-white.png"
                alt=""
              />
              <img
                v-else
                src="@/assets/images/delete.png"
                class="action-img"
                alt="删除"
                @click="handleDelete(scope, 'contractInfos')"
              />
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 物流信息 -->
      <div class="info-card">
        <span class="card-title">物流信息</span>
        <div v-if="!isViewMode" class="add-btn">
          <el-button type="text" icon="el-icon-plus" @click="handleAdd('logisticsInfos')"
            >新增物流信息</el-button
          >
        </div>
        <el-table :data="logisticsInfos" border style="width: 100%" class="table_Header">
          <el-table-column label="物流类型" width="140">
            <template slot-scope="scope">
              <div class="table-form-item">
                <el-select
                  v-model="scope.row.logisticsType"
                  :disabled="isViewMode"
                  placeholder="请选择"
                  size="mini"
                  style="width: 100%"
                >
                  <el-option label="海运" value="ocean_carry"></el-option>
                </el-select>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="提单号" min-width="140">
            <template slot-scope="scope">
              <div class="table-form-item">
                <el-input
                  v-model="scope.row.eblNo"
                  :disabled="isViewMode"
                  placeholder="请输入"
                  size="mini"
                ></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="船公司" min-width="140">
            <template slot-scope="scope">
              <el-input
                :disabled="isViewMode"
                v-model="scope.row.carrier"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="发货人" min-width="140">
            <template slot-scope="scope">
              <el-input
                :disabled="isViewMode"
                v-model="scope.row.shipper"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="发货港" min-width="140">
            <template slot-scope="scope">
              <el-input
                :disabled="isViewMode"
                v-model="scope.row.dispatchPort"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="装货日期" min-width="140">
            <template slot-scope="scope">
              <el-date-picker
                :disabled="isViewMode"
                v-model="scope.row.onBoardDate"
                type="date"
                placeholder="请选择"
                size="mini"
                style="width: 100%"
                value-format="yyyy-MM-dd"
              ></el-date-picker>
            </template>
          </el-table-column>
          <el-table-column label="收货人" min-width="140">
            <template slot-scope="scope">
              <el-input
                :disabled="isViewMode"
                v-model="scope.row.consignee"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="到货港" min-width="140">
            <template slot-scope="scope">
              <el-input
                :disabled="isViewMode"
                v-model="scope.row.arrivePort"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="附件" min-width="140">
            <template slot-scope="scope">
              <input
                :ref="'logisticsFile' + scope.$index"
                accept=".pdf,.jpg,.png,.jpeg"
                type="file"
                style="display: none"
                @change="(e) => uploadContractFile(scope, 'logisticsInfos', e)"
              />
              <el-button
                v-if="!isViewMode && !scope.row.attachments?.[0]?.id"
                size="mini"
                type="text"
                @click="uploadContract(scope, 'logisticsFile')"
              >
                上传
              </el-button>
              <div v-else class="table-cell-value">
                <BaseUpload
                  :businessOtherFiles="scope.row.attachments"
                  :isViewMode="isViewMode"
                  needDelete
                  @deleteAttachment="deleteAttachment(scope, 'logisticsInfos')"
                ></BaseUpload>
              </div>
            </template>
          </el-table-column>
          <el-table-column v-if="!isViewMode" label="操作" width="80">
            <template slot-scope="scope">
              <img
                v-if="scope.$index === 0"
                class="action-img"
                src="@/assets/images/delete-white.png"
                alt=""
              />
              <img
                v-else
                src="@/assets/images/delete.png"
                class="action-img"
                alt="删除"
                @click="handleDelete(scope, 'logisticsInfos')"
              />
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 发票信息 -->
      <div class="info-card">
        <span class="card-title">发票信息</span>
        <div v-if="!isViewMode" class="add-btn">
          <el-button type="text" icon="el-icon-plus" @click="handleAdd('invoiceInfos')"
            >新增发票信息</el-button
          >
        </div>
        <el-table :data="invoiceInfos" border style="width: 100%" class="table_Header">
          <el-table-column label="发票编号" min-width="140">
            <template slot-scope="scope">
              <div class="table-form-item">
                <el-input
                  v-model="scope.row.invoiceNo"
                  :disabled="isViewMode"
                  placeholder="请输入"
                  size="mini"
                ></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="日期" min-width="140">
            <template slot-scope="scope">
              <el-date-picker
                :disabled="isViewMode"
                v-model="scope.row.invoiceDate"
                type="date"
                placeholder="请选择"
                size="mini"
                style="width: 100%"
                value-format="yyyy-MM-dd"
              ></el-date-picker>
            </template>
          </el-table-column>
          <el-table-column label="付款单位" min-width="140">
            <template slot-scope="scope">
              <el-input
                :disabled="isViewMode"
                v-model="scope.row.payAgency"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="收款单位" min-width="140">
            <template slot-scope="scope">
              <el-input
                :disabled="isViewMode"
                v-model="scope.row.receiveAgency"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="金额(万)" min-width="140">
            <template slot-scope="scope">
              <el-input
                :disabled="isViewMode"
                v-model="scope.row.amount"
                placeholder="请输入"
                size="mini"
                type="number"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="币种" min-width="140">
            <template slot-scope="scope">
              <el-select
                :disabled="isViewMode"
                v-model="scope.row.currencyType"
                placeholder="请选择"
                size="mini"
                style="width: 100%"
              >
                <el-option
                  v-for="item in currencyTypeParam"
                  :key="item.detailId"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="附件" min-width="140">
            <template slot-scope="scope">
              <input
                :ref="'invoiceFile' + scope.$index"
                accept=".pdf,.jpg,.png,.jpeg"
                type="file"
                style="display: none"
                @change="(e) => uploadContractFile(scope, 'invoiceInfos', e)"
              />
              <el-button
                v-if="!isViewMode && !scope.row.attachments?.[0]?.id"
                size="mini"
                type="text"
                @click="uploadContract(scope, 'invoiceFile')"
              >
                上传
              </el-button>
              <div v-else class="table-cell-value">
                <BaseUpload
                  :businessOtherFiles="scope.row.attachments"
                  :isViewMode="isViewMode"
                  needDelete
                  @deleteAttachment="deleteAttachment(scope, 'invoiceInfos')"
                ></BaseUpload>
              </div>
            </template>
          </el-table-column>
          <el-table-column v-if="!isViewMode" label="操作" width="80">
            <template slot-scope="scope">
              <img
                v-if="scope.$index === 0"
                class="action-img"
                src="@/assets/images/delete-white.png"
                alt=""
              />
              <img
                v-else
                src="@/assets/images/delete.png"
                class="action-img"
                alt="删除"
                @click="handleDelete(scope, 'invoiceInfos')"
              />
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 企业申报其他附件 -->
      <div class="info-card">
        <span class="card-title">企业申报其他附件(例如收入申报单)</span>
        <BaseUpload
          :businessOtherFiles="businessOtherFiles"
          :isViewMode="isViewMode"
          @changeFileList="(value) => changeFileList(value, 'businessOtherFiles')"
          @removeFile="(value) => removeFile(value, 'businessOtherFiles')"
        ></BaseUpload>
      </div>

      <!-- 底部操作按钮 -->
    </div>
  </div>
</template>

<script>
import { uploadApi } from '@/apis/upload'
import { getCompanyListDataApi, editBusinessDataApi, getBusinessDataApi } from '@/apis/index'
import { mapGetters } from 'vuex'
import BaseUpload from '@/component/BaseUpload.vue'
import moment from 'moment'

export default {
  name: 'TradeDetail',
  components: {
    BaseUpload
  },
  data() {
    return {
      businessNo: '',
      mode: 'view', // view: 查看, edit: 修改, create: 新增
      detailData: {
        businessNo: '',
        enterpriseCode: '',
        companyName: '',
        targetBankId: '',
        creditCode: ''
      },
      // 基本信息表单校验规则
      basicFormRules: {
        targetBankId: [{ required: true, message: '请选择授权银行', trigger: 'change' }]
      },
      // 表格校验错误信息
      tableValidationErrors: {
        contractInfos: [],
        logisticsInfos: [],
        invoiceInfos: []
      },
      contractInfos: [
        {
          attachments: [],
          contractType: '',
          contractNo: '',
          contractDate: '',
          purchaser: '',
          seller: '',
          declaredAmount: '',
          taxSavingAmount: '',
          currencyType: '',
          amount: '',
          contractNature: ''
        }
      ],
      logisticsInfos: [
        {
          logisticsType: '',
          eblNo: '',
          carrier: '',
          shipper: '',
          dispatchPort: '',
          onBoardDate: '',
          consignee: '',
          arrivePort: '',
          attachments: []
        }
      ],
      invoiceInfos: [
        {
          invoiceNo: '',
          invoiceDate: '',
          payAgency: '',
          receiveAgency: '',
          amount: '',
          currencyType: '',
          attachments: []
        }
      ],
      businessOtherFiles: [],
      declarationFiles: [],
      bankList: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      }
    }
  },
  computed: {
    ...mapGetters(['getUser', 'currencyTypeParam', 'contractTypeParam']),
    isViewMode() {
      return this.mode === 'view'
    },
    isEditMode() {
      return this.mode === 'edit'
    },
    isCreateMode() {
      return this.mode === 'create'
    },
    pageTitle() {
      switch (this.mode) {
        case 'create':
          return '新增业务申报'
        case 'edit':
          return '修改业务申报'
        case 'view':
        default:
          return '业务申报基本信息'
      }
    }
  },
  watch: {
    getUser: {
      handler(newVal) {
        this.detailData.companyName = newVal?.companyName
        this.detailData.creditCode = newVal?.creditCode
      },
      immediate: true
    }
  },
  mounted() {
    this.businessNo = this.$route.params.id
    this.mode = this.$route.query.mode || 'view'
    this.$dispatch('enum/getCurrencyTypeParam')
    this.$dispatch('enum/getContractTypeParam')
    this.querySearchAsync()
    // 如果是新增模式，businessNo 为 'new'
    if (this.businessNo === 'new') {
      this.mode = 'create'
      this.businessNo = ''
    } else {
      this.getDetailData()
    }
  },
  methods: {
    getDetailData() {
      getBusinessDataApi(this.businessNo).then((res) => {
        if (res.code === 200) {
          console.log(res)
          const { data } = res
          this.detailData = {
            ...data,
            ...this.detailData,
            targetBankId: data.targetBankId,
            financialInstitutionCode: data.targetBankInfo?.financialInstitutionCode
          }

          if (data.businessOtherFiles?.length) {
            this.businessOtherFiles = data.businessOtherFiles.map((e) => {
              return { name: e.filename, ...e }
            })
          }

          this.contractInfos = data.contractInfos
          this.logisticsInfos = data.logisticsInfos
          this.invoiceInfos = data.invoiceInfos
        }
      })
    },
    querySearchAsync() {
      getCompanyListDataApi({ companyName: '' }, '104').then((res) => {
        if (res.code === 200) {
          this.bankList = res.data
        } else {
          this.bankList = []
        }
      })
    },
    handleSelectBank(value) {
      const findObj = this.bankList.find((item) => item.id === value)
      if (findObj?.id) {
        this.detailData.financialInstitutionCode = findObj.financialInstitutionCode
      } else {
        this.detailData.financialInstitutionCode = ''
      }
    },
    goBack() {
      this.$router.go(-1)
    },
    uploadContract(scope, refStr) {
      this.$refs[`${refStr}${scope.$index}`].click()
    },
    uploadContractFile(scope, str, e) {
      const file = e.target.files[0]
      if (!file) return
      // 文件类型校验
      const validTypes = ['application/pdf', 'image/jpg', 'image/jpeg', 'image/png']
      const validExtensions = ['.pdf', '.jpg', '.jpeg', '.png']

      // 获取文件扩展名
      const fileName = file.name.toLowerCase()
      const fileExtension = fileName.substring(fileName.lastIndexOf('.'))

      if (!validTypes.includes(file.type) || !validExtensions.includes(fileExtension)) {
        this.$message.error('只能上传PDF、JPG、PNG格式的文件')
        e.target.value = '' // 清空文件选择
        return
      }
      const formData = new FormData()
      formData.append('file', file)
      formData.append('isTemp', false)
      uploadApi(formData).then((res) => {
        this.$set(this[str][scope.$index], 'attachments', [res.data])
      })
    },
    // 删除表格行的附件
    deleteAttachment(scope, str) {
      this.$set(this[str][scope.$index], 'attachments', [])
    },
    changeFileList(data, array) {
      this[array].push(data)
    },
    removeFile(fileList, array) {
      this[array] = fileList
    },
    handleAdd(str) {
      if (str === 'contractInfos') {
        if (this.contractInfos.length >= 2) {
          this.$message.warning('最多只能添加两个合同信息')
          return
        }
        this.contractInfos.push({
          contractType: '',
          contractNo: '',
          contractDate: '',
          purchaser: '',
          seller: '',
          declaredAmount: '',
          taxSavingAmount: '',
          currencyType: '',
          amount: '',
          contractNature: '',
          attachments: []
        })
      } else if (str === 'logisticsInfos') {
        this.logisticsInfos.push({
          logisticsType: '',
          eblNo: '',
          carrier: '',
          shipper: '',
          dispatchPort: '',
          onBoardDate: '',
          consignee: '',
          arrivePort: '',
          attachments: []
        })
      } else if (str === 'invoiceInfos') {
        this.invoiceInfos.push({
          invoiceNo: '',
          invoiceDate: '',
          payAgency: '',
          receiveAgency: '',
          amount: '',
          currencyType: '',
          attachments: []
        })
      }
    },
    async handleSave(type) {
      const contractInfos = this.contractInfos.map((item) => ({
        ...item,
        contractDate: item.contractDate
          ? moment(item.contractDate).format('YYYY-MM-DD 00:00:00')
          : ''
      }))
      const logisticsInfos = this.logisticsInfos.map((item) => ({
        ...item,
        onBoardDate: item.onBoardDate ? moment(item.onBoardDate).format('YYYY-MM-DD 00:00:00') : ''
      }))
      const invoiceInfos = this.invoiceInfos.map((item) => ({
        ...item,
        invoiceDate: item.invoiceDate ? moment(item.invoiceDate).format('YYYY-MM-DD 00:00:00') : ''
      }))
      const data = {
        id: this.businessNo === 'new' ? '' : this.businessNo,
        targetBankId: this.detailData.targetBankId,
        contractInfos: contractInfos,
        logisticsInfos: logisticsInfos,
        invoiceInfos: invoiceInfos,
        businessOtherFiles: this.businessOtherFiles,
        action: type
      }
      if (type === 'submit') {
        this.$confirm('是否确认提交，提交后内容无法修改', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            // 确认提交
            const basicFormValid = await this.validateBasicForm()
            if (!basicFormValid) {
              this.$message.error('请完善基本信息')
              return
            }

            // 校验表格数据
            const tableValid = this.validateAllTables()
            if (!tableValid) {
              this.$message.error('请完善表格中的信息')
              return
            }
            editBusinessDataApi(data).then((res) => {
              if (res.code === 200) {
                this.goBack()
              }
            })
          })
          .catch(() => {
            // 取消提交
            return
          })
      } else {
        editBusinessDataApi(data).then((res) => {
          if (res.code === 200) {
            this.goBack()
          }
        })
      }
    },
    handleDelete(scope, str) {
      this[str].splice(scope.$index, 1)
      // 删除对应的校验错误信息
      if (this.tableValidationErrors[str] && this.tableValidationErrors[str][scope.$index]) {
        this.tableValidationErrors[str].splice(scope.$index, 1)
      }
    },

    // 校验基本信息表单
    validateBasicForm() {
      return new Promise((resolve) => {
        this.$refs.basicForm.validate((valid) => {
          resolve(valid)
        })
      })
    },

    // 校验表格字段
    validateTableField(tableName, rowIndex, fieldName, value) {
      const rules = this.getFieldRules(tableName, fieldName)

      if (!rules || rules.length === 0) return

      // 确保错误信息数组存在
      if (!this.tableValidationErrors[tableName]) {
        this.$set(this.tableValidationErrors, tableName, [])
      }
      if (!this.tableValidationErrors[tableName][rowIndex]) {
        this.$set(this.tableValidationErrors[tableName], rowIndex, {})
      }

      // 校验规则
      let errorMessage = ''

      console.log(rules)

      for (const rule of rules) {
        console.log(rule, value)

        if (
          rule.required &&
          (!value || (Array.isArray(value) && value.length === 0) || value.toString().trim() === '')
        ) {
          errorMessage = rule.message || `${fieldName}不能为空`
          break
        }
        if (rule.type === 'number' && value && isNaN(Number(value))) {
          errorMessage = rule.message || `${fieldName}必须是数字`
          break
        }
        if (rule.min && value && value.toString().length < rule.min) {
          errorMessage = rule.message || `${fieldName}长度不能少于${rule.min}位`
          break
        }
        if (rule.max && value && value.toString().length > rule.max) {
          errorMessage = rule.message || `${fieldName}长度不能超过${rule.max}位`
          break
        }
      }

      // 设置错误信息
      this.$set(this.tableValidationErrors[tableName][rowIndex], fieldName, errorMessage)
    },

    // 获取字段错误信息
    getFieldError(tableName, rowIndex, fieldName) {
      return this.tableValidationErrors[tableName]?.[rowIndex]?.[fieldName] || ''
    },

    // 获取字段校验规则
    getFieldRules(tableName, fieldName) {
      const rulesMap = {
        contractInfos: {
          contractType: [{ required: true, message: '请选择合同类型' }],
          contractNo: [{ required: true, message: '请输入合同编号' }],
          contractDate: [{ required: true, message: '请选择书立日期' }],
          purchaser: [{ required: true, message: '请输入购买方' }],
          seller: [{ required: true, message: '请输入销售方' }],
          declaredAmount: [{ required: true, message: '请输入申报金额' }],
          taxSavingAmount: [{ required: true, message: '请输入减免金额' }],
          currencyType: [{ required: true, message: '请选择合同币种' }],
          amount: [
            { required: true, message: '请输入合同金额' },
            { type: 'number', message: '合同金额必须是数字' }
          ],
          attachments: [{ required: true, message: '请上传合同附件' }]
        },
        invoiceInfos: {
          invoiceNo: [{ required: true, message: '请输入发票编号' }],
          invoiceDate: [{ required: true, message: '请选择发票日期' }],
          payAgency: [{ required: true, message: '请输入付款单位' }],
          receiveAgency: [{ required: true, message: '请输入收款单位' }],
          amount: [
            { required: true, message: '请输入金额' },
            { type: 'number', message: '金额必须是数字' }
          ],
          currencyType: [{ required: true, message: '请选择币种' }],
          attachments: [{ required: true, message: '请上传附件' }]
        }
      }

      return rulesMap[tableName]?.[fieldName] || []
    },

    // 校验所有表格
    validateAllTables() {
      let isValid = true

      // 校验合同信息
      this.contractInfos.forEach((item, index) => {
        Object.keys(item).forEach((key) => {
          this.validateTableField('contractInfos', index, key, item[key])
          if (this.getFieldError('contractInfos', index, key)) {
            isValid = false
          }
        })
      })

      // 校验发票信息
      this.invoiceInfos.forEach((item, index) => {
        Object.keys(item).forEach((key) => {
          this.validateTableField('invoiceInfos', index, key, item[key])
          if (this.getFieldError('invoiceInfos', index, key)) {
            isValid = false
          }
        })
      })

      return isValid
    }
  }
}
</script>

<style lang="scss" scoped>
.trade-detail-container {
  padding: 20px;
  background-color: #fff;
  margin-top: 20px;
}

.page-header {
  border-bottom: 1px solid #ededed;
  margin: 0 -20px 20px;
  padding: 0 20px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .back-btn {
    font-size: 20px;
  }
}

.info-card {
  margin-bottom: 20px;

  .card-title {
    display: inline-block;
    font-size: 18px;
    border-left: 3px solid #3566f4;
    font-weight: bold;
    color: #17233d;
    padding-left: 5px;
    margin-bottom: 10px;
  }
}

.detail-form {
  .form-value {
    color: #606266;
    line-height: 40px;
    display: inline-block;
    min-height: 40px;
    padding: 0 15px;
    background-color: #f5f7fa;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    width: 100%;
    box-sizing: border-box;
  }

  .el-input,
  .el-select {
    width: 100%;
  }
}

.footer-actions {
  text-align: center;
  .el-button {
    margin: 0 10px;
    min-width: 100px;
  }
}

.table-cell-value {
  display: inline-block;
  padding: 0 8px;
  border-radius: 4px;
  width: 100%;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  .attachments-img {
    width: 16px;
    height: 16px;
  }
  .file-value {
    display: inline-block;
    color: #3566f4;
    width: 80%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .el-icon-close {
    cursor: pointer;
  }
}

::v-deep .div__header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
}

::v-deep .el-table {
  .el-button--text {
    color: #409eff;

    &:hover {
      color: #66b1ff;
    }
  }

  .el-table__cell {
    padding: 8px 0;
  }

  .el-input--mini .el-input__inner,
  .el-select--mini .el-input__inner,
  .el-date-editor--mini .el-input__inner {
    height: 32px;
    line-height: 32px;
  }
}

.add-btn {
  display: flex;
  justify-content: flex-end;
}
.action-img {
  width: 24px;
  height: 24px;
  cursor: pointer;
}

// 表格表单校验样式
.table-form-item {
  position: relative;

  &.is-error {
    .el-input__inner,
    .el-select .el-input__inner,
    .el-date-editor .el-input__inner {
      border-color: #f56c6c;
    }
  }

  .el-form-item__error {
    color: #f56c6c;
    font-size: 12px;
    line-height: 1;
    padding-top: 4px;
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1;
    background: #fff;
    padding: 2px 4px;
    border-radius: 2px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    white-space: nowrap;
  }
}

// 表格行高度调整，为错误信息留出空间
::v-deep .el-table .el-table__cell {
  padding: 12px 0;
}
</style>

