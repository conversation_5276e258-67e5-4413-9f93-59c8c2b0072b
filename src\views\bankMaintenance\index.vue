<template>
  <div>
    <h3>商业银行维护</h3>
    <div class="enterprise-list">
      <!-- 顶部表单区域 -->
      <el-form :inline="true" class="demo-form-inline" label-position="top">
        <el-form-item label="银行名称">
          <el-autocomplete
            v-model="searchForm.companyName"
            style="width: 100%"
            :fetch-suggestions="querySearchAsync"
            value-key="companyName"
            value="id"
            :debounce="500"
            placeholder="请输入银行名称"
            @select="handleSelectCompany"
          ></el-autocomplete>
            <!-- <el-input v-model="searchForm.companyName" placeholder="请输入银行名称"></el-input> -->
        </el-form-item>
        <el-form-item label="金融机构编码">
            <el-input v-model="searchForm.financialInstitutionCode" placeholder="请输入金融机构编码"></el-input>
        </el-form-item>
        <el-form-item label="银行类型">
            <el-select v-model="searchForm.bankType" clearable placeholder="请选择银行类型">
            <el-option v-for="(item,index) in bankTypeList"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
            </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getTableData">查询</el-button>
        </el-form-item>
      </el-form>
  
      <!-- 表格区域 -->
      <div>
        <div class="batch-opt">
          <el-button type="primary" @click="handleAddCompany">
            新增
          </el-button>
        </div>
        <el-table
          border
          empty-text
          class="table_Header"
          :data="tableData" >
            <el-table-column prop="companyName" label="银行名称" show-overflow-tooltip width="180"></el-table-column>
            <el-table-column prop="financialInstitutionCode" show-overflow-tooltip label="金融机构编码" width="180"></el-table-column>
            <el-table-column prop="companyTypeName" show-overflow-tooltip label="银行类型" width="160"></el-table-column>
            <el-table-column prop="contactPerson" show-overflow-tooltip label="联系人"></el-table-column>
            <el-table-column prop="contactPhone" show-overflow-tooltip label="联系电话"></el-table-column>
            <el-table-column prop="email" label="电子邮箱" show-overflow-tooltip width="250"></el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template slot-scope="scope">
              <el-tooltip content="详情" placement="top">
                <img
                  class="table-icon"
                  :src="require('@/assets/images/view.png')"
                  alt=""
                  @click="viewDetail(scope.row)"
                />
              </el-tooltip>
              <el-tooltip content="编辑" placement="top">
                <img
                  class="table-icon"
                  :src="require('@/assets/images/edit.png')"
                  alt=""
                  @click="editCompany(scope.row)"
                />
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <img
                  class="table-icon"
                  :src="require('@/assets/images/delete.png')"
                  alt=""
                  @click="deleteCompany(scope.row)"
                />
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-wrapper">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total">
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
  
  <script>
  import { dictBankType } from '@/apis/enum'
  import { getCompanyListApi,deleteCompanyApi,getOrganListDataApi } from '@/apis/index'
  export default {
    data() {
      return {
        bankTypeList:[],
        searchForm: {
          companyName: '',
            financialInstitutionCode: '',
            bankType: ''
        },
        tableData: [],
        currentPage: 1,
        pageSize: 10,
        total: 0
      };
    },
    watch: {
      'searchForm.companyName': function (val) {
        if (!val) {
          this.searchForm.businessId = ''
        }
      }
    },
    async mounted() {
      const res = await dictBankType();
      // this.bankTypeList = res.data.details
      this.bankTypeList = [
        { label: '全部', value: '' }, // 添加“全部”选项
        ...res.data.details
      ];
      await this.getTableData()
    },
    methods: {
      querySearchAsync(queryString, cb) {
        getOrganListDataApi(104,{ companyName: queryString }).then((res) => {
          cb(res.data)
        })
      },
      handleSelectCompany(value) {
        if (value?.id) {
          this.searchForm.businessId = value.id
        } else {
          this.searchForm.businessId = ''
        }
      },
      async getTableData() {
        // 获取表格数据逻辑
        const res =  await getCompanyListApi({
          pageNum: this.currentPage,
          pageSize: this.pageSize,
          companyName: this.searchForm.companyName,
          code: this.searchForm.financialInstitutionCode,
          companyType: this.searchForm.bankType,
          roleType:'104'
        })
        this.tableData = res.data.rows
        this.total = res.data.total
      },
      handleAddCompany() {
        this.$router.push({
          name: 'bankDetail',
          query: {
            isAdd: true,
          }
        });
      },
      viewDetail(row) {
        this.$router.push({
          name: 'bankDetail',
          query: {
            id: row.id
          }
        });
      },
      editCompany(row) {
        this.$router.push({
          name: 'bankDetail',
          query: {
            id: row.id,
            isEdit: true,
          }
        });
      },
      deleteCompany(row) {
        console.log(row, "row");
        this.$confirm(`是否确认删除`+ row.companyName +"企业？" ,'提示', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          const params = {
            id: row.id
          }
          const res = await deleteCompanyApi(params)
          if (res.code == 200) {
            this.$message.success('删除成功')
            this.getTableData()
          }
        }).catch(() => {
        });
      },
      handleSizeChange(val) {
        this.pageSize = val;
        this.getTableData();
      },
      handleCurrentChange(val) {
        this.currentPage = val;
        this.getTableData();
      },
    }
  };
  </script>
  
  <style scoped>
  .enterprise-list {
    padding: 20px;
    border-radius: 8px;
    background-color: #FFFFFF;
    margin-top: 20px;
    }
  .demo-form-inline {
    margin-bottom: 20px;
    display: flex;
    align-items: flex-end;
  }
  .table-icon{
    width: 24px;
  }
  .batch-opt{
    background-color: #FAFBFC;
    border: 1px solid #EDEDED;
    border-bottom:none;
    padding: 10px 30px;
    text-align: end;
  }

  </style>
  <style lang="scss" scoped>
    /deep/.el-table__body-wrapper {
      min-height: 500px;
}
</style>