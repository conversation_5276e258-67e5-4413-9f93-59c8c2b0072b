<template>
  <div>
    <h3>申报信息审核</h3>
    <div class="trade-detail-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <div class="back-btn">
          <span @click="goBack">
            <i class="el-icon-arrow-left"></i> 业务编号：{{ detailData.reportNo }}
          </span>
          <span v-if="mode === 'edit'" class="status-box">待审核</span>
        </div>
        <div v-if="detailData?.auditStatus === 'wait'" class="footer-actions">
          <el-button size="small" @click="goBack">取消</el-button>
          <el-button size="small" type="primary" @click="showDialog">审核</el-button>
        </div>
      </div>

      <!-- 业务申报基本信息 -->
      <div class="info-card">
        <span class="card-title">业务核验基本信息</span>

        <el-form :model="detailData" label-width="120px" label-position="top" class="detail-form">
          <el-row :gutter="24">
            <el-col :span="6">
              <el-form-item label="业务编号">
                <el-input
                  v-model="detailData.reportNo"
                  disabled
                  placeholder="请输入业务编号"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="6">
              <el-form-item label="企业名称">
                <el-input
                  disabled
                  v-model="detailData.companyName"
                  placeholder="请输入企业名称"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="企业组织机构代码">
                <el-input
                  disabled
                  v-model="detailData.creditCode"
                  placeholder="请输入企业组织机构代码"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="授权银行">
                <el-input
                  disabled
                  v-model="detailData.bankName"
                  placeholder="请选择银行代码名称"
                  style="width: 100%"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="金融机构代码">
                <el-input
                  disabled
                  v-model="detailData.financialInstitutionCode"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 合同信息 -->
      <div class="info-card">
        <span class="card-title">合同信息</span>
        <el-table :data="contractInfos" border style="width: 100%" class="table_Header">
          <el-table-column label="合同类型" width="180">
            <template slot-scope="scope">
              <el-select
                disabled
                v-model="scope.row.contractType"
                placeholder="请选择"
                size="mini"
                style="width: 100%"
              >
                <el-option
                  v-for="item in contractTypeParam"
                  :key="item.detailId"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="合同编号" width="190">
            <template slot-scope="scope">
              <el-input
                disabled
                v-model="scope.row.contractNo"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="书立日期" width="160">
            <template slot-scope="scope">
              <el-date-picker
                disabled
                v-model="scope.row.contractDate"
                type="date"
                placeholder="请选择"
                size="mini"
                style="width: 100%"
                value-format="yyyy-MM-dd"
              ></el-date-picker>
            </template>
          </el-table-column>
          <el-table-column label="购买方">
            <template slot-scope="scope">
              <el-input
                disabled
                v-model="scope.row.purchaser"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="销售方">
            <template slot-scope="scope">
              <el-input
                disabled
                v-model="scope.row.seller"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="合同币种" width="160">
            <template slot-scope="scope">
              <el-select
                disabled
                v-model="scope.row.currencyType"
                placeholder="请选择"
                size="mini"
                style="width: 100%"
              >
                <el-option
                  v-for="item in currencyTypeParam"
                  :key="item.detailId"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="合同金额" width="140">
            <template slot-scope="scope">
              <el-input
                disabled
                v-model="scope.row.amount"
                placeholder="请输入"
                size="mini"
                type="number"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="合同附件" width="180">
            <template slot-scope="scope">
              <div class="table-cell-value">
                <BaseUpload :businessOtherFiles="scope.row.attachments" isViewMode></BaseUpload>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 物流信息 -->
      <div class="info-card">
        <span class="card-title">物流信息</span>
        <el-table :data="logisticsInfos" border style="width: 100%" class="table_Header">
          <el-table-column label="物流类型" width="180">
            <template slot-scope="scope">
              <el-select
                disabled
                v-model="scope.row.logisticsType"
                placeholder="请选择"
                size="mini"
                style="width: 100%"
              >
                <el-option label="海运" value="ocean_carry"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="提单号" width="190">
            <template slot-scope="scope">
              <el-input
                disabled
                v-model="scope.row.eblNo"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="船公司">
            <template slot-scope="scope">
              <el-input
                disabled
                v-model="scope.row.carrier"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="发货人">
            <template slot-scope="scope">
              <el-input
                disabled
                v-model="scope.row.shipper"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="发货港">
            <template slot-scope="scope">
              <el-input
                disabled
                v-model="scope.row.dispatchPort"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="装货日期">
            <template slot-scope="scope">
              <el-date-picker
                disabled
                v-model="scope.row.onBoardDate"
                type="date"
                placeholder="请选择"
                size="mini"
                style="width: 100%"
                value-format="yyyy-MM-dd"
              ></el-date-picker>
            </template>
          </el-table-column>
          <el-table-column label="收货人">
            <template slot-scope="scope">
              <el-input
                disabled
                v-model="scope.row.consignee"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="到货港">
            <template slot-scope="scope">
              <el-input
                disabled
                v-model="scope.row.arrivePort"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="附件">
            <template slot-scope="scope">
              <div class="table-cell-value">
                <BaseUpload :businessOtherFiles="scope.row.attachments" isViewMode></BaseUpload>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 发票信息 -->
      <div class="info-card">
        <span class="card-title">发票信息</span>

        <el-table :data="invoiceInfos" border style="width: 100%" class="table_Header">
          <el-table-column label="发票编号" width="180">
            <template slot-scope="scope">
              <el-input
                disabled
                v-model="scope.row.invoiceNo"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="日期" width="190">
            <template slot-scope="scope">
              <el-date-picker
                disabled
                v-model="scope.row.invoiceDate"
                type="date"
                placeholder="请选择"
                size="mini"
                style="width: 100%"
                value-format="yyyy-MM-dd"
              ></el-date-picker>
            </template>
          </el-table-column>
          <el-table-column label="付款单位">
            <template slot-scope="scope">
              <el-input
                disabled
                v-model="scope.row.payAgency"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="收款单位">
            <template slot-scope="scope">
              <el-input
                disabled
                v-model="scope.row.receiveAgency"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="金额">
            <template slot-scope="scope">
              <el-input
                disabled
                v-model="scope.row.amount"
                placeholder="请输入"
                size="mini"
                type="number"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="币种">
            <template slot-scope="scope">
              <el-select
                disabled
                v-model="scope.row.currencyType"
                placeholder="请选择"
                size="mini"
                style="width: 100%"
              >
                <el-option
                  v-for="item in currencyTypeParam"
                  :key="item.detailId"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="附件">
            <template slot-scope="scope">
              <div class="table-cell-value">
                <BaseUpload :businessOtherFiles="scope.row.attachments" isViewMode></BaseUpload>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="info-card">
        <span class="card-title">企业申报其他附件</span>
        <div>
          <BaseUpload :businessOtherFiles="businessOtherFiles" isViewMode></BaseUpload>
        </div>
      </div>
      <el-dialog title="审批" :visible.sync="dialogFormVisible" width="40%">
        <el-form :model="form" label-position="top">
          <el-form-item label="审批结果">
            <el-radio v-model="form.action" label="pass">通过</el-radio>
            <el-radio v-model="form.action" label="reject">不通过</el-radio>
          </el-form-item>
          <el-form-item v-if="form.action === 'reject'">
            <el-input
              v-model="form.remark"
              type="textarea"
              placeholder="请输入审批结果"
              rows="5"
            ></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button :disabled="!form.action" type="primary" @click="submitHandler">
            确 定
          </el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { getBusinessDataApi, tradeAuditApi } from '@/apis/index'
import { mapGetters } from 'vuex'
import BaseUpload from '@/component/BaseUpload.vue'

export default {
  name: 'InformationReviewDetail',
  components: { BaseUpload },
  data() {
    return {
      id: '',
      detailData: {},
      contractInfos: [],
      logisticsInfos: [],
      invoiceInfos: [],
      attachmentData: [],
      paymentData: [],
      dialogFormVisible: false,
      form: {
        name: ''
      },
      mode: 'view',
      businessOtherFiles: []
    }
  },
  computed: {
    ...mapGetters(['currencyTypeParam', 'contractTypeParam'])
  },
  created() {
    this.id = this.$route.params.id
    this.mode = this.$route.query.mode || 'view'
    this.$dispatch('enum/getCurrencyTypeParam')
    this.$dispatch('enum/getContractTypeParam')

    this.loadDetailData()
  },
  methods: {
    loadDetailData() {
      getBusinessDataApi(this.id).then((res) => {
        if (res.code === 200) {
          console.log(res)
          const { data } = res
          this.detailData = {
            ...data,
            ...this.detailData,
            id: data.targetBankId,
            companyName: data.businessInfo.companyName,
            creditCode: data.businessInfo.creditCode,
            bankName: data.targetBankInfo.companyName,
            financialInstitutionCode: data.targetBankInfo.financialInstitutionCode
          }
          this.businessOtherFiles = data.businessOtherFiles?.map((e) => {
            return { name: e.filename, ...e }
          })
          this.contractInfos = data.contractInfos
          this.logisticsInfos = data.logisticsInfos
          this.invoiceInfos = data.invoiceInfos
        }
      })
    },
    goBack() {
      this.$router.go(-1)
    },
    showDialog() {
      this.dialogFormVisible = true
    },
    submitHandler() {
      const param = {
        id: this.id,
        action: this.form.action,
        remark: this.form.action === 'reject' ? this.form.remark : ''
      }
      tradeAuditApi(param).then((res) => {
        if (res.code === 200) {
          this.$message({
            message: '审核成功',
            type: 'success'
          })
          this.goBack()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.trade-detail-container {
  padding: 20px;
  background-color: #fff;
  margin-top: 20px;
}

.page-header {
  border-bottom: 1px solid #ededed;
  margin: 0 -20px 20px;
  padding: 0 20px 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .back-btn {
    font-size: 20px;
    height: 34px;
    display: flex;
    align-items: center;
    .status-box {
      margin-left: 20px;
      font-size: 14px;
      display: inline-block;
      height: 100%;
      line-height: 34px;
      padding: 0 10px;
      border-radius: 4px;
      background-color: #f5faff;
      color: #1659ce;
    }
  }
}

.info-card {
  margin-bottom: 20px;

  .card-title {
    display: inline-block;
    font-size: 18px;
    border-left: 3px solid #3566f4;
    font-weight: bold;
    color: #17233d;
    padding-left: 5px;
    margin-bottom: 30px;
  }
  .verification-info {
    margin-top: 30px;
    p {
      color: #17233d;
      font-size: 14px;
      margin-bottom: 10px;
    }
  }
}

.detail-form {
  .form-value {
    color: #606266;
    line-height: 40px;
    display: inline-block;
    min-height: 40px;
    padding: 0 15px;
    background-color: #f5f7fa;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    width: 100%;
    box-sizing: border-box;
  }

  .el-input,
  .el-select {
    width: 100%;
  }
}

.footer-actions {
  text-align: center;
  .el-button {
    margin: 0 10px;
    min-width: 100px;
  }
}

.table-cell-value {
  display: inline-block;
  padding: 0 8px;
  border-radius: 4px;
  width: 100%;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  .attachment-img {
    width: 16px;
    height: 16px;
  }
  .file-value {
    display: inline-block;
    color: #3566f4;
    width: 80%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .el-icon-close {
    cursor: pointer;
  }
}

::v-deep .div__header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
}

::v-deep .el-table {
  .el-button--text {
    color: #409eff;

    &:hover {
      color: #66b1ff;
    }
  }

  .el-table__cell {
    padding: 8px 0;
  }

  .el-input--mini .el-input__inner,
  .el-select--mini .el-input__inner,
  .el-date-editor--mini .el-input__inner {
    height: 32px;
    line-height: 32px;
  }
}
::v-deep {
  .el-upload-dragger,
  .el-upload {
    width: 100%;
  }
  .el-upload-dragger {
    display: flex;
    justify-content: center;
    align-content: center;
    flex-direction: column;
    .el-upload__text {
      font-weight: bold;
      font-size: 20px;
      margin-bottom: 20px;
    }
  }
}
.add-btn {
  display: flex;
  justify-content: flex-end;
}
.action-img {
  width: 24px;
  height: 24px;
}
</style>
