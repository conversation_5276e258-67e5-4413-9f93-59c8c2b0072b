<template>
    <div class="basic-info">
      <el-form label-position="top" :model="editableData" ref="editableData">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="info-item">
              <div class="label">税务登记证号:</div>
              <el-form-item prop="taxRegistrationNumber">
                <div v-if="!isEdit" class="value">{{ enterpriseData.taxRegistrationNumber || "/" }}</div>
                <div v-else class="value">
                    <el-input v-model="editableData.taxRegistrationNumber" placeholder="请输入税务登记证号"></el-input> 
                </div>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="label">纳税人识别号:</div>
              <el-form-item prop="taxpayerIdentificationNumber">
                <div v-if="!isEdit" class="value">{{ enterpriseData.taxpayerIdentificationNumber || "/" }}</div>
                <div v-else class="value">
                    <el-input v-model="editableData.taxpayerIdentificationNumber" placeholder="请输入纳税人识别号"></el-input>
                </div>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="label">注册币种：</div>
              <el-form-item prop="registeredCurrency">
                <div v-if="!isEdit" class="value">{{ enterpriseData.registeredCurrency || "/" }}</div>
                <div v-else class="value">
                  <el-select v-model="editableData.registeredCurrency" style="width: 100%;" placeholder="请选择注册币种">
                    <el-option
                      v-for="item in currencyList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                      </el-option>
                  </el-select>
                </div>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="label">注册资本币种：</div>
              <el-form-item prop="registeredCapitalCurrency">
                <div v-if="!isEdit" class="value">{{ enterpriseData.registeredCapitalCurrency || "/" }}</div>
                <div v-else class="value">
                  <el-select v-model="editableData.registeredCapitalCurrency" style="width: 100%;" placeholder="请选择注册币种">
                    <el-option
                      v-for="item in currencyList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                      </el-option>
                  </el-select>
                </div>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="info-item">
              <div class="label">最近年度审计报告：</div>
              <el-form-item prop="latestAuditReport">
                <div v-if="!isEdit" >
                  <div v-for="(item,index) in enterpriseData.latestAuditReport" :key="index" @click="viewImg(item)" class="value">
                    <div>{{ item.docName}}</div>
                  </div>
                </div>
                <div v-else class="value">
                  <FileUpload
                    v-model="editableData.latestAuditReport"
                    :action=action
                    :headers="headers"
                    :limit=8
                  />
                </div>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="label">最近年度纳税证明：</div>
              <el-form-item prop="latestTaxProof">
                <div v-if="!isEdit" >
                  <div v-for="(item,index) in enterpriseData.latestTaxProof" :key="index" @click="viewImg(item)" class="value">
                    <div>{{ item.docName}}</div>
                  </div>
                </div>
                <div v-else class="value">
                  <FileUpload
                    v-model="editableData.latestTaxProof"
                    :action=action
                    :headers="headers"
                    :limit=8
                  />
                </div>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <el-dialog
        class="viewImg"
        :before-close="closeDialog"
        :visible.sync="dialogVisible"
        append-to-body
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
      <div style="height:100%">
        <img v-if="imageType != 'application/pdf'" width="100%" :src="dialogImageUrl" alt="" />
        <object
          v-else
          :data="dialogImageUrl"
          type="application/pdf"
          width="100%"
          height="100%"
        ></object>
      </div>
    </el-dialog>
    </div>
  </template>
  
  <script>
  import Cookies from 'js-cookie'
  import { getCurrencyTypeParamApi } from '@/apis/enum'
  import FileUpload from '@/component/FileUpload.vue';
  export default {
    props: {
      enterpriseData: {
        type: Object,
        required: true,
      },
      financialInfoFormData: {
        type: Object,
        required: true,
        },
      isEdit: {
        type: Boolean,
        default: false,
      },
    },
    components: {
      FileUpload,
    },
    data() {
        return {
          editableData: {},
          currencyList:[],
          headers: {Authorization: decodeURIComponent(Cookies.get('AMS-SIMPLE-ADMIN-TRADE-TOKEN'))},
          action: '/apis/system/file/upload',
          dialogImageUrl: '',
          imageUrl: '',
          imageType: '',
          dialogVisible: false,
          dialogImgVisible: false,
          disabled: false,
      };
    },
    watch: {
      financialInfoFormData: {
          handler(newVal) {
            this.editableData = { ...newVal };
            this.editableData.latestAuditReport = this.enterpriseData.latestAuditReport;
          },
          deep: true,
        },
    },
    async created() {
      this.editableData = { ...this.financialInfoFormData };
      const res = await getCurrencyTypeParamApi();
      this.currencyList = res.data.details
    },
    methods: {
      getFormData() {
        return this.editableData;
      },
      handleSave() {
        this.$emit('save', this.editableData);
      },
      viewImg(val) {
        console.log(val, '----');
        this.dialogVisible = true
        this.dialogImageUrl = val.previewUrl
        this.imageType = val.type
      },
      closeDialog(){
        this.dialogVisible = false
      },
    },
  };
  </script>
  
  <style scoped>
  .basic-info{
    margin-top: 10px;
  }
  .basic-info .info-item {
    margin-bottom: 30px;
  }
  
  .basic-info .label {
    color: #515A6E;
    font-weight: 400;
    font-size: 14px;
  }
  
  .basic-info .value {
    margin-top: 10px;
    color: #17233D;
    font-weight: 400;
    font-size: 14px;
  }
  </style>
    <style lang="scss" scoped>
    /deep/.el-form-item__content {
      line-height: normal;
    }
    /deep/ .el-form-item__label{
      padding: 0;
    }
    </style>