import apiClient from '@/utils/request'
import Cookies from 'js-cookie'
let authType = Cookies.get('authType')
console.log(authType)

// 获取列表信息
export function getTradeListDataApi(param) {
  return apiClient.get(`/validation/trade/${authType}/page`, { params: param })
}

// 模糊查询企业列表信息
export function getCompanyListDataApi(param, roleId = 102) {
  return apiClient.get(`/system/organ/${roleId}/list`, { params: param, noLoading: true })
}

// 新增，编辑，删除详情信息
export function editBusinessDataApi(data) {
  return apiClient.post(`/validation/trade/business`, data)
}
// 获取详情信息
export function getBusinessDataApi(id) {
  return apiClient.get(`/validation/trade/${id}?roleId=${authType}`)
}

// 运维审核
export function tradeAuditApi(data) {
  return apiClient.post(`/validation/trade/audit`, data)
}

//企业创建
export function createCompanyApi(data) {
  return apiClient.post(`/system/organ/create`, data)
}

//企业查询列表
export function getCompanyListApi(param) {
  return apiClient.get(`/system/organ/list`, { params: param })
}

//企业查询列表
export function getCompanyDetailApi(id) {
  return apiClient.get(`/system/organ/${id}/detail`)
}

//修改企业
export function updateCompanyDetailApi(data) {
  return apiClient.post(`/system/organ/update`, data)
}

//修改企业
export function deleteCompanyApi(data) {
  return apiClient.post(`/system/organ/delete`, data)
}

//获取绑定DID二维码
export function getDIDCodeApi() {
  return apiClient.get(`/system/organ/applyBusinessLicense`)
}

//轮询获取航贸通APP扫码授权后信息
export function generateDidQrCode(param) {
  return apiClient.get(`/system/organ/businessLicense`, { params: param })
}

//用户列表
export function getUserList(param) {
  return apiClient.get(`/system/user/list`, { params: param })
}

//新增用户
export function createUser(data) {
  return apiClient.post(`/system/user/create`, data)
}

//修改用户
export function updateUser(data) {
  return apiClient.post(`/system/user/update`, data)
}

//删除用户
export function deleteUser(data) {
  return apiClient.post(`/system/user/delete`, data)
}

//启用状态变更
export function changeActivate(data) {
  return apiClient.post(`/system/user/activate`, data)
}
// 银行重复校验
export function bankTradeCheckApi(data) {
  return apiClient.post(`/validation/trade/check`, data)
}

// 银行核验结果提交
export function bankTradeSubmitApi(data) {
  return apiClient.post(`/validation/trade/bank`, data)
}

// 税务局批复
export function opinionTradeSubmitApi(data) {
  return apiClient.post(`/validation/trade/opinion`, data)
}

// 核验报告查询
export function getReportListApi(param) {
  return apiClient.get(`/validation/trade/common/page`, { params: param })
}

// 复议申请
export function reviewTradeSubmitApi(data) {
  return apiClient.post(`/validation/trade/review`, data)
}
