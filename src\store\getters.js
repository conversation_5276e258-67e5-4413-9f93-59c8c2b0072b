const getters = {
  getUser: (state) => {
    return state.auth.user
  },
  isNormal: (state) => {
    return state.authType === 'Normal'
  },
  sidebar: (state) => state.app.sidebar,
  authType: (state) => state.auth.authType,
  userRole: (state) => {
    return state.auth.user?.role || state.auth.user?.userType || state.auth.authType
  },
  userInfo: (state) => {
    return {
      user: state.auth.user,
      authType: state.auth.authType,
      role: state.auth.user?.role || state.auth.user?.userType || state.auth.authType
    }
  },
  tradeStatusParam: (state) => state.enum.tradeStatusParam,
  contractTypeParam: (state) => state.enum.contractTypeParam,
  currencyTypeParam: (state) => state.enum.currencyTypeParam,
  settlementDirectionParam: (state) => state.enum.settlementDirectionParam
}
export default getters
