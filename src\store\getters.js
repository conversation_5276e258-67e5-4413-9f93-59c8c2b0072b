const getters = {
  getUser: (state) => {
    return state.auth.user
  },
  isNormal: (state) => {
    return state.authType === 'Normal'
  },
  sidebar: (state) => state.app.sidebar,
  authType: (state) => state.auth.authType,
  tradeStatusParam: (state) => state.enum.tradeStatusParam,
  contractTypeParam: (state) => state.enum.contractTypeParam,
  currencyTypeParam: (state) => state.enum.currencyTypeParam,
  settlementDirectionParam: (state) => state.enum.settlementDirectionParam
}
export default getters
