<template>
  <div style="width: 100%;">
    <el-upload
      v-if="!isViewMode && !needDelete"
      class="upload-demo"
      drag
      :http-request="customUpload"
      multiple
      action="#"
      :file-list="businessOtherFiles"
      :on-remove="handleRemove"
      :on-preview="handlePreview"
    >
      <div class="el-upload__text">
        <p style="font-size: 24px; margin-bottom: 20px; line-height: 30px">
          点击或拖拽文件到此处上传
        </p>
        <p style="line-height: 30px; font-size: 18px; color: #515a6e; font-weight: normal">
          支持的文件格式:PDF、JPG、PNG
        </p>
      </div>
    </el-upload>
    <template v-else>
      <div
        class="file-item"
        v-for="item in businessOtherFiles"
        :key="item.id"
        @click="viewImg(item)"
      >
        <img src="@/assets/images/attachment.png" alt="" style="width: 14px; height: 14px" />
        <span class="file-value">{{ item.filename }}</span>
        <i v-if="!isViewMode && needDelete" class="el-icon-close" @click="deleteAttachment()"></i>
      </div>
    </template>

    <!-- 预览弹窗 -->
    <el-dialog :visible.sync="previewDialogVisible" append-to-body>
      <div :class="previewUrl.indexOf('application/pdf') === -1 ? 'img-preview' : 'pdf-preview'">
        <img
          v-if="previewUrl.indexOf('application/pdf') === -1"
          :src="previewUrl"
          style="width: 100%; height: auto"
        />
        <object
          v-else
          :data="previewUrl"
          type="application/pdf"
          width="100%"
          height="100%"
        ></object>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { uploadApi, downloadApi } from '@/apis/upload'
export default {
  name: 'TradeUpload',
  props: {
    businessOtherFiles: {
      type: Array,
      default: () => []
    },
    isViewMode: {
      type: Boolean,
      default: false
    },
    needDelete: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      previewDialogVisible: false,
      previewUrl: '',
      previewType: 'image',
      arrayList: []
    }
  },

  methods: {
    customUpload(file) {
      const { file: rawFile } = file

      // 文件类型校验
      const validTypes = ['application/pdf', 'image/jpg', 'image/jpeg', 'image/png']
      const validExtensions = ['.pdf', '.jpg', '.jpeg', '.png']
      const fileName = rawFile.name.toLowerCase()
      const fileExtension = fileName.substring(fileName.lastIndexOf('.'))

      if (!validTypes.includes(rawFile.type) || !validExtensions.includes(fileExtension)) {
        this.$message.error('只能上传PDF、JPG、PNG格式的文件')
        return
      }

      const formData = new FormData()
      formData.append('file', rawFile)
      formData.append('isTemp', false)

      uploadApi(formData)
        .then((res) => {
          let reader = new FileReader()
          reader.readAsDataURL(rawFile)
          reader.onload = () => {
            this.$emit('changeFileList', {
              ...res.data,
              name: res.data.filename,
              fileData: reader.result
            })
          }
          this.$message.success('上传成功')
        })
        .catch(() => {
          this.$message.error('上传失败')
        })
    },
    handleRemove(file, fileList) {
      console.log(file, fileList)

      this.$emit('removeFile', [...fileList])
    },
    handlePreview(file) {
      if (file.fileData) {
        this.previewUrl = file.fileData
      } else {
        this.previewUrl = file.url
      }
      this.previewDialogVisible = true
    },
    async viewImg(file) {
      if (file.contentType === 'application/pdf') {
        let proRes = await downloadApi({ id: file.id })
        // 将文件流转换为base64
        const reader = new FileReader()
        reader.readAsDataURL(proRes)
        reader.onload = () => {
          this.previewUrl = reader.result
        }
      } else {
        this.previewUrl = file.url
      }
      this.previewDialogVisible = true
    },
    deleteAttachment() {
      this.$emit('deleteAttachment')
    }
  }
}
</script>

<style lang="scss" scoped>
.upload-file-item {
  position: relative;
  width: 100%;
  height: 100%;
}

.el-upload-list__item-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.img-preview {
  height: 100%;
}
.pdf-preview {
  height: 600px;
}
::v-deep {
  .el-upload-dragger,
  .el-upload {
    width: 100%;
  }
  .el-upload-dragger {
    display: flex;
    justify-content: center;
    align-content: center;
    flex-direction: column;
    .el-upload__text {
      font-weight: bold;
      font-size: 20px;
      margin-bottom: 20px;
    }
  }
  .el-upload--picture-card {
    height: 180px;
  }
  .el-dialog__header {
    padding: 25px;
  }
}
.file-item {
  margin-bottom: 10px;
  font-size: 14px;
  color: #3566f4;
  cursor: pointer;
  display: flex;
  align-items: center;
  width: 100%;
}
.file-value {
  display: inline-block;
  color: #3566f4;
  width: 80%;
  margin-left: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>

