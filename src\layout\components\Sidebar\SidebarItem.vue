<template>
  <div v-if="!item.hidden" class="menu-wrapper">
    <!-- 没有子菜单的情况 -->
    <template
      v-if="
        hasOneShowingChild(item.children, item) &&
        (!onlyOneChild.children || onlyOneChild.noShowingChildren) &&
        !item.alwaysShow
      "
    >
      <app-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path)">
        <el-menu-item
          :index="resolvePath(onlyOneChild.path)"
          :title="isCollapse ? onlyOneChild.meta.title : ''"
        >
          <i v-if="onlyOneChild.meta.icon" :class="onlyOneChild.meta.icon"></i>
          <span v-if="!isCollapse">{{ onlyOneChild.meta.title }}</span>
        </el-menu-item>
      </app-link>
    </template>

    <!-- 有子菜单的情况 -->
    <el-submenu
      v-else
      :index="resolvePath(item.path)"
      :title="isCollapse && item.meta ? item.meta.title : ''"
    >
      <template slot="title">
        <i v-if="item.meta && item.meta.icon" :class="item.meta.icon"></i>
        <span v-if="item.meta && item.meta.title && !isCollapse">{{ item.meta.title }}</span>
      </template>

      <sidebar-item
        v-for="child in item.children"
        :key="child.path"
        :item="child"
        :base-path="resolvePath(child.path)"
      />
    </el-submenu>
  </div>
</template>

<script>
import AppLink from './Link'
import path from 'path'

export default {
  name: 'SidebarItem',
  components: { AppLink },
  props: {
    item: {
      type: Object,
      required: true
    },
    basePath: {
      type: String,
      default: ''
    },
    isCollapse: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      onlyOneChild: null
    }
  },
  methods: {
    hasOneShowingChild(children = [], parent) {
      const showingChildren = children.filter((item) => {
        if (item.hidden) {
          return false
        } else {
          // 临时设置
          this.onlyOneChild = item
          return true
        }
      })

      // 当只有一个子路由时，默认显示子路由
      if (showingChildren.length === 1) {
        return true
      }

      // 没有子路由则显示父路由
      if (showingChildren.length === 0) {
        this.onlyOneChild = { ...parent, path: '', noShowingChildren: true }
        return true
      }

      return false
    },
    resolvePath(routePath) {
      if (this.isExternalLink(routePath)) {
        return routePath
      }
      if (this.isExternalLink(this.basePath)) {
        return this.basePath
      }
      return path.resolve(this.basePath, routePath)
    },
    isExternalLink(path) {
      return /^(https?:|mailto:|tel:)/.test(path)
    }
  }
}
</script>

<style lang="scss" scoped>
.menu-wrapper {
  .el-menu-item,
  .el-submenu__title {
    &:hover {
      background-color: #fff !important;
    }
  }
  ::v-deep {
    .el-menu {
      background-color: #fff !important;
    }
    .el-icon-arrow-right {
      display: none;
    }
    .el-submenu__title,
    .el-menu-item {
      font-size: 16px !important;
      background-color: #fff !important;
      color: #222 !important;
      &.is-active {
        color: #3566f4 !important;
        background-color: #f8f8f9 !important;
      }
      &:hover {
        color: #3566f4 !important;
        background-color: #f8f8f9 !important;
      }
    }
  }
}
</style>

