<template>
  <div>
    <h3>账号管理</h3>
    <div class="account-management">
      <!-- 搜索表单 -->
      <el-form :inline="true" :model="searchForm" class="search-form" label-position="top">
        <el-form-item label="姓名">
          <el-input v-model="searchForm.name" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="登录用户名">
          <el-input v-model="searchForm.loginName" placeholder="请输入登录用户名"></el-input>
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="searchForm.type" clearable placeholder="全部">
            <el-option v-for="(item,index) in typeOptions" 
              :key="index"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="启用状态">
          <el-select v-model="searchForm.status" clearable placeholder="全部">
            <el-option label="全部" value=""></el-option>
            <el-option label="启用" value="0"></el-option>
            <el-option label="禁用" value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="">
          <el-button type="primary" @click="handleSearch">查询</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <div class="add-btn">
        <el-button type="primary" @click="handleCreate">新增账号</el-button>
      </div>
      <el-table :data="tableData" border empty-text class="table_Header" style="width: 100%">
        <el-table-column label="序号" align="center" width="55">
          <template slot-scope="scope">
            <span>{{ scope.$index +( pagination.currentPage - 1) * pagination.pageSize + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="realName" show-overflow-tooltip width="120" label="姓名"></el-table-column>
        <el-table-column prop="username" show-overflow-tooltip width="120" label="登录用户名"></el-table-column>
        <el-table-column prop="email"  show-overflow-tooltip label="邮箱" width="180"></el-table-column>
        <el-table-column prop="phone" show-overflow-tooltip label="手机号" width="180"></el-table-column>
        <el-table-column prop="organRoleName" show-overflow-tooltip width="120" label="类型"></el-table-column>
        <el-table-column prop="organName" show-overflow-tooltip label="所属企业 / 所属银行" width="180"></el-table-column>
        <el-table-column label="启用状态">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.isEnabled"
              @change="checkSwitch(scope.row)"
              active-color="#13ce66"
              inactive-color="#ff4949"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template slot-scope="scope">
            <el-tooltip content="编辑" placement="top">
              <img
                class="action-img"
                src="@/assets/images/edit.png"
                alt="编辑"
                @click="handleEdit(scope.row)"
              />
            </el-tooltip>
            <el-tooltip v-if="!scope.row.passwordModified" content="发送邮件" placement="top">
              <img
                class="action-img"
                :src="require('@/assets/images/send.png')"
                alt=""
                @click="handleSendEmail(scope.row)"
              />
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
                <img
                  class="action-img"
                  :src="require('@/assets/images/delete.png')"
                  alt=""
                  @click="handleDelete(scope.row)"
                />
              </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total">
        </el-pagination>
      </div>
      <el-dialog :title="dialogTitle" @close="closeDialog" :close-on-click-modal="false" :visible.sync="dialogFormVisible" width="50%">
        <el-form class="account-form" ref="accountFormRef" :rules="accountRule" :model="accountForm" label-position="top">
          <el-form-item label="姓名" prop="realName">
            <el-input v-model="accountForm.realName" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="登录用户名" prop="username">
            <el-input :disabled="isEdit" v-model="accountForm.username" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="accountForm.email" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="phone">
            <el-input v-model="accountForm.phone" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="类型" prop="organRoleType">
            <el-select :disabled="isEdit" v-model="accountForm.organRoleType" placeholder="请选择" style="width: 100%">
              <el-option
                v-for="item in typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="accountForm.organRoleType === '102'" label="所属企业" prop="company">
            <el-select :disabled="isEdit" v-model="accountForm.company" placeholder="请选择" style="width: 100%">
              <el-option
                v-for="item in enterpriseTypeList"
                :key="item.id"
                :label="item.companyName"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="accountForm.organRoleType === '104'" label="所属银行" prop="bank">
            <el-select :disabled="isEdit" v-model="accountForm.bank" placeholder="请选择" style="width: 100%">
              <el-option
                v-for="item in bankTypeList"
                :key="item.id"
                :label="item.companyName"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="closeDialog()">取 消</el-button>
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { sendEmail } from '@/apis/index';
import { dictOrganType } from '@/apis/enum'
  import { verifyNumber, verifyEmail } from '@/assets/js/common'
import { getUserList,getUserDetail,createUser,updateUser,getOrganListDataApi,deleteUser,changeActivate } from '@/apis/index'
export default {
  data() {
    return {
      options:[],
      typeOptions: [],
      bankTypeList:[],
      enterpriseTypeList: [],
      searchForm: {
        name: '',
        loginName: '',
        type: '',
        status: ''
      },
      tableData: [],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 1
      },
      dialogFormVisible: false,
      accountForm: {
        realName: '',
        username: '',
        email: '',
        phone: '',
        organRoleType: '',
        company: '',
        bank: ''
      },
      dialogTitle: '新增账号',
      isEdit: false,
      accountRule: {
        realName: [
          { required: true, message: '请输入姓名', trigger: 'blur' }
        ],
        username: [
          { required: true, message: '登录用户名', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          { validator: verifyEmail, trigger: 'blur', }
        ],
        phone: [
          { validator: verifyNumber, trigger: 'blur', }
        ],
        organRoleType: [
          { required: true, message: '请选择类型', trigger: 'change' }
        ],
        company: [
          { required: true, message: '请选择所属企业', trigger: 'change' }
        ],
        bank: [
          { required: true, message: '请选择所属银行', trigger: 'change' }
        ],
      }
    }
  },
  async mounted() {
    const typeRes = await dictOrganType();
    const bankRes = await getOrganListDataApi(104);
    const enterpriseRes = await getOrganListDataApi(102);
    this.bankTypeList = bankRes.data
    this.enterpriseTypeList = enterpriseRes.data
    this.typeOptions = typeRes.data.details
    await this.getTableData()
  },
  methods: {
    handleSearch() {
      this.getTableData()
    },
    getTableData() {
      const data = {
        userName:this.searchForm.loginName,
        realName:this.searchForm.name,
        roleType:this.searchForm.type,
        activateStatus:this.searchForm.status,
        pageNum: this.pagination.currentPage,
        pageSize: this.pagination.pageSize
      }
      getUserList(data).then(res => {
        if (res.code == 200) {
          this.tableData = res.data.rows.map(item => {
            return {...item, isEnabled: item.activateStatus == 1 ? false : true}
          })
          this.pagination.total = res.data.total
        }
      })
    },
    checkSwitch(row) {
      this.$confirm(row.isEnabled ? '确定启用此账号' : '确定停止使用此账号', "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: 'warning'
      }).then(() => {
          const params = {
            id: row.userId,
            activateStatus: row.isEnabled ? 0 : 1,
          };
        changeActivate(params).then((res) => {
            if (res.code == 200) {
              this.$message.success("操作成功")
            } else {
              row.isEnabled = !row.isEnabled
            }
          })
      }).catch(() => {
          row.isEnabled = !row.isEnabled;
          this.$message.info("已取消");
      });
    },
    handleEdit(row) {
      // 编辑逻辑
      this.dialogTitle = '编辑'
      this.dialogFormVisible = true
      this.isEdit = true
      getUserDetail({ userId: row.userId }).then(res => {
        this.accountForm = res.data
        this.accountForm.userId = row.userId
        this.accountForm.organRoleType = this.accountForm.userType
        if (res.data.userType == '102' || res.data.userType == '104') {
          this.accountForm.company = res.data.organization.id
          this.accountForm.bank = res.data.organization.id
        }
      })
    },
    handleSendEmail(val) {
      sendEmail({
        id: val.userId
      }).then(res => {
        if (res.code == 200) {
          this.$message({
            type: 'success',
            message: '发送成功'
          })
        }
      })
    },
    handleDelete(val) {
      this.$confirm('确定删除该账号吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteUser({
          id: val.userId
        }).then(res => {
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: '删除成功'
            })
            this.getTableData()
          }
        })
      }).catch(() => {
      });
    },
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.getTableData()
      // 更新数据
    },
    handleCurrentChange(page) {
      this.pagination.currentPage = page
      this.getTableData()
      // 更新数据
    },
    handleCreate() {
      this.dialogTitle = '新增账号'
      this.dialogFormVisible = true
      this.isEdit = false
    },
    // 关闭弹框方法
    closeDialog() {
      this.dialogFormVisible = false;
      this.isEdit = false
      // 重置表单和验证状态
      if (this.$refs.accountFormRef) {
        this.$refs.accountFormRef.resetFields();
      }
      // 手动重置非表单项字段
      this.accountForm = {
        realName: '',
        username: '',
        email: '',
        phone: '',
        organRoleType: '',
        company: '',
        bank: ''
      };
    },
    handleSubmit() {
      this.$refs.accountFormRef.validate(valid => {
        if (valid) {
          // 校验通过，执行保存逻辑
          const data = {
            realName: this.accountForm.realName,
            username: this.accountForm.username,
            userType: this.accountForm.organRoleType,
            email: this.accountForm.email,
            phone: this.accountForm.phone,
            orgId: this.accountForm.organRoleType == '102' ? this.accountForm.company : this.accountForm.organRoleType == '104' ? this.accountForm.bank : '',
          }
          if (!this.isEdit) {
            createUser(data).then(res => {
              if (res.code === 200) {
                this.$message.success('添加成功')
                this.closeDialog()
                this.getTableData()
              }
            })
          } else {
            data.id = this.accountForm.userId
            updateUser(data).then(res => {
              if (res.code === 200) {
                this.$message.success('修改成功')
                this.closeDialog()
                this.getTableData()
              }
            })
          }
        } else {
          // 校验失败，阻止提交
          return false;
        }
      });
    }
  }
}
</script>

<style scoped lang="scss">
.account-management {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  margin-top: 20px;
}
.search-form {
  margin-bottom: 20px;
  display: flex;
  align-items: flex-end;
}
.pagination-container {
  margin-top: 20px;
  text-align: right;
}
.action-img {
  width: 24px;
  height: 24px;
  margin-right: 10px;
}
.add-btn {
  background-color: #FAFBFC;
  border: 1px solid #EDEDED;
  border-bottom:none;
  padding: 10px 30px;
  text-align: end;
}
</style>
<style lang="scss" scoped>
/deep/.el-table__body-wrapper {
  min-height: 460px;
}
/deep/.el-dialog__body{
  padding: 30px 60px;
}
</style>
