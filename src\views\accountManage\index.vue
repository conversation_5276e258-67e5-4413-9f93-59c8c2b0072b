<template>
  <div>
    <h3>账号管理</h3>
    <div class="account-management">
      <!-- 搜索表单 -->
      <el-form :inline="true" :model="searchForm" class="search-form" label-position="top">
        <el-form-item label="姓名">
          <el-input v-model="searchForm.name" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="登录用户名">
          <el-input v-model="searchForm.loginName" placeholder="请输入登录用户名"></el-input>
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="searchForm.type" placeholder="全部">
            <el-option v-for="(item,index) in typeOptions" 
              :key="index"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="启用状态">
          <el-select v-model="searchForm.status" placeholder="全部">
            <el-option label="全部" value=""></el-option>
            <el-option label="启用" value="1"></el-option>
            <el-option label="禁用" value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="">
          <el-button type="primary" @click="handleSearch">查询</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <div class="add-btn">
        <el-button type="primary" @click="handleCreate">新增账号</el-button>
      </div>
      <el-table :data="tableData" border empty-text class="table_Header" style="width: 100%">
        <el-table-column prop="id" label="序号" width="50"></el-table-column>
        <el-table-column prop="name" label="姓名"></el-table-column>
        <el-table-column prop="loginName" label="登录用户名"></el-table-column>
        <el-table-column prop="email" label="邮箱"></el-table-column>
        <el-table-column prop="phone" label="手机号"></el-table-column>
        <el-table-column prop="type" label="类型"></el-table-column>
        <el-table-column prop="company" label="所属企业 / 所属银行"></el-table-column>
        <el-table-column label="启用状态">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.enabled"
              active-color="#13ce66"
              inactive-color="#ff4949"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180">
          <template slot-scope="scope">
            <img
              class="action-img"
              src="@/assets/images/edit.png"
              alt="编辑"
              @click="handleEdit(scope.row)"
            />
            <img class="action-img" src="@/assets/images/send.png" alt="发送邮件" />
            <img
              class="action-img"
              src="@/assets/images/delete.png"
              alt=""
              @click="handleDelete(scope.row)"
            />
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
      <el-dialog :title="dialogTitle" :visible.sync="dialogFormVisible" width="40%">
        <el-form :model="accountForm" label-position="top">
          <el-form-item label="姓名" required>
            <el-input v-model="accountForm.name" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="登录用户名" required>
            <el-input v-model="accountForm.userName" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="邮箱" required>
            <el-input v-model="accountForm.email" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="手机号" required>
            <el-input v-model="accountForm.phone" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="类型" required>
            <el-select v-model="accountForm.type" placeholder="请选择" style="width: 100%">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属企业/所属银行" required>
            <el-select v-model="accountForm.type" placeholder="请选择" style="width: 100%">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="dialogFormVisible = false">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { dictBankType,dictEnterpriseType } from '@/apis/enum'
import { getUserList } from '@/apis/index'
export default {
  data() {
    return {
      options:[],
      typeOptions: [],
      searchForm: {
        name: '',
        loginName: '',
        type: '',
        status: ''
      },
      tableData: [],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 1
      },
      dialogFormVisible: false,
      accountForm: {},
      dialogTitle: '新增账号'
    }
  },
  async mounted() {
    const bankRes = await dictBankType();
    const enterpriseRes = await dictEnterpriseType();
    this.typeOptions = [...bankRes.data.details, ...enterpriseRes.data.details]
    await this.getTableData()
  },
  methods: {
    handleSearch() {
      // 查询逻辑
    },
    getTableData() {
      const data = {
        userName:this.searchForm.name,
        realName:this.searchForm.loginName,
        roleType:this.searchForm.type,
        activateStatus:this.searchForm.status,
        pageNum: this.pagination.currentPage,
        pageSize: this.pagination.pageSize
      }
      getUserList(data).then(res => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.pagination.total = res.data.total
        }
      })
    },
    handleEdit(row) {
      // 编辑逻辑
      this.dialogTitle = '新增账号'
      this.dialogFormVisible = true
      this.detailId = row.id
    },
    handleDelete() {
      // 删除逻辑
    },
    handleSizeChange(size) {
      this.pagination.pageSize = size
      // 更新数据
    },
    handleCurrentChange(page) {
      this.pagination.currentPage = page
      // 更新数据
    },
    handleCreate() {
      this.dialogTitle = '新增账号'
      this.dialogFormVisible = true
      this.detailId = ''
    }
  }
}
</script>

<style scoped lang="scss">
.account-management {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  margin-top: 20px;
}
.search-form {
  margin-bottom: 20px;
  display: flex;
  align-items: flex-end;
}
.pagination-container {
  margin-top: 20px;
  text-align: right;
}
.action-img {
  width: 24px;
  height: 24px;
  margin-right: 10px;
  cursor: pointer;
}
.add-btn {
  background-color: #FAFBFC;
  border: 1px solid #EDEDED;
  border-bottom:none;
  padding: 10px 30px;
  text-align: end;
}
</style>
<style lang="scss" scoped>
/deep/.el-table__body-wrapper {
  min-height: 500px;
}
</style>
