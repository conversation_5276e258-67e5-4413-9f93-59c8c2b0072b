<template>
  <div>
    <h3>离岸贸易业务核验</h3>
    <div class="trade-detail-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <div class="back-btn">
          <span @click="goBack">
            <i class="el-icon-arrow-left"></i> 业务编号：{{ detailData.reportNo }}
          </span>
        </div>
        <div v-if="!disabledEdit" class="footer-actions">
          <el-button size="small" @click="goBack">取消</el-button>
          <el-button size="small" type="primary" @click="submitHandler('save')">保存</el-button>
          <el-button size="small" type="primary" @click="submitHandler('submit')">提交</el-button>
        </div>
      </div>

      <!-- 业务申报基本信息 -->
      <div class="info-card">
        <span class="card-title">业务核验基本信息</span>

        <el-form :model="detailData" label-width="120px" label-position="top" class="detail-form">
          <el-row :gutter="24">
            <el-col :span="6">
              <el-form-item label="业务编号">
                <el-input
                  v-model="detailData.reportNo"
                  disabled
                  placeholder="请输入业务编号"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="6">
              <el-form-item label="企业名称">
                <el-input
                  disabled
                  v-model="detailData.targetBankId"
                  placeholder="请输入企业名称"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="企业组织机构代码">
                <el-input
                  disabled
                  v-model="detailData.financialInstitutionCode"
                  placeholder="请输入企业组织机构代码"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="授权银行">
                <el-select
                  disabled
                  v-model="detailData.targetBankId"
                  placeholder="请选择银行代码名称"
                  style="width: 100%"
                >
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="金融机构代码">
                <el-input
                  disabled
                  v-model="detailData.financialInstitutionCode"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 合同信息 -->
      <div class="info-card">
        <span class="card-title">合同信息</span>
        <el-table :data="contractInfos" border style="width: 100%" class="table_Header">
          <el-table-column label="合同类型" width="180">
            <template slot-scope="scope">
              <el-select
                disabled
                v-model="scope.row.contractType"
                placeholder="请选择"
                size="mini"
                style="width: 100%"
              >
                <el-option
                  v-for="item in contractTypeParam"
                  :key="item.detailId"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="合同编号" width="190">
            <template slot-scope="scope">
              <el-input
                disabled
                v-model="scope.row.contractNo"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="购买方">
            <template slot-scope="scope">
              <el-input
                disabled
                v-model="scope.row.purchaser"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="销售方">
            <template slot-scope="scope">
              <el-input
                disabled
                v-model="scope.row.seller"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="合同币种" width="160">
            <template slot-scope="scope">
              <el-select
                disabled
                v-model="scope.row.currencyType"
                placeholder="请选择"
                size="mini"
                style="width: 100%"
              >
                <el-option
                  v-for="item in currencyTypeParam"
                  :key="item.detailId"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="合同金额" width="140">
            <template slot-scope="scope">
              <el-input
                disabled
                v-model="scope.row.amount"
                placeholder="请输入"
                size="mini"
                type="number"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="合同附件" width="180">
            <template slot-scope="scope">
              <div class="table-cell-value">
                <img src="@/assets/images/attachment.png" alt="" class="attachment-img" />
                <span class="file-value">{{ scope.row.attachments[0]?.filename || '-' }}</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="verification-info">
          <p>核验结果说明</p>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-input
                v-model="bankContractDesc"
                :disabled="disabledEdit"
                type="textarea"
                placeholder="请输入核验结果说明"
                rows="5"
              ></el-input>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 物流信息 -->
      <div class="info-card">
        <span class="card-title">物流信息</span>
        <el-table :data="logisticsInfos" border style="width: 100%" class="table_Header">
          <el-table-column label="物流类型" width="180">
            <template slot-scope="scope">
              <el-select
                disabled
                v-model="scope.row.logisticsType"
                placeholder="请选择"
                size="mini"
                style="width: 100%"
              >
                <el-option label="海运" value="ocean_carry"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="提单号" width="190">
            <template slot-scope="scope">
              <el-input
                disabled
                v-model="scope.row.eblNo"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="船公司">
            <template slot-scope="scope">
              <el-input
                disabled
                v-model="scope.row.carrier"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="发货人">
            <template slot-scope="scope">
              <el-input
                disabled
                v-model="scope.row.shipper"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="发货港">
            <template slot-scope="scope">
              <el-input
                disabled
                v-model="scope.row.dispatchPort"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="装货日期">
            <template slot-scope="scope">
              <el-date-picker
                disabled
                v-model="scope.row.onBoardDate"
                type="date"
                placeholder="请选择"
                size="mini"
                style="width: 100%"
                value-format="yyyy-MM-dd"
              ></el-date-picker>
            </template>
          </el-table-column>
          <el-table-column label="收货人">
            <template slot-scope="scope">
              <el-input
                disabled
                v-model="scope.row.consignee"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="到货港">
            <template slot-scope="scope">
              <el-input
                disabled
                v-model="scope.row.arrivePort"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="附件">
            <template slot-scope="scope">
              <div class="table-cell-value">
                <img src="@/assets/images/attachment.png" alt="" class="attachment-img" />
                <span class="file-value">{{ scope.row.attachments[0]?.filename || '-' }}</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="verification-info">
          <p>核验结果说明</p>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-input
                v-model="bankLogisticsDesc"
                :disabled="disabledEdit"
                type="textarea"
                placeholder="请输入核验结果说明"
                rows="5"
              ></el-input>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 发票信息 -->
      <div class="info-card">
        <span class="card-title">发票信息</span>

        <el-table :data="invoiceInfos" border style="width: 100%" class="table_Header">
          <el-table-column label="发票编号" width="180">
            <template slot-scope="scope">
              <el-input
                disabled
                v-model="scope.row.invoiceNo"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="日期" width="190">
            <template slot-scope="scope">
              <el-date-picker
                disabled
                v-model="scope.row.invoiceDate"
                type="date"
                placeholder="请选择"
                size="mini"
                style="width: 100%"
                value-format="yyyy-MM-dd"
              ></el-date-picker>
            </template>
          </el-table-column>
          <el-table-column label="付款单位">
            <template slot-scope="scope">
              <el-input
                disabled
                v-model="scope.row.payAgency"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="收款单位">
            <template slot-scope="scope">
              <el-input
                disabled
                v-model="scope.row.receiveAgency"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="金额">
            <template slot-scope="scope">
              <el-input
                disabled
                v-model="scope.row.amount"
                placeholder="请输入"
                size="mini"
                type="number"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="币种">
            <template slot-scope="scope">
              <el-select
                disabled
                v-model="scope.row.currencyType"
                placeholder="请选择"
                size="mini"
                style="width: 100%"
              >
                <el-option
                  v-for="item in currencyTypeParam"
                  :key="item.detailId"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="附件">
            <template slot-scope="scope">
              <div class="table-cell-value">
                <img src="@/assets/images/attachment.png" alt="" class="attachment-img" />
                <span class="file-value">{{ scope.row.attachments[0]?.filename || '-' }}</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="verification-info">
          <p>核验结果说明</p>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-input
                v-model="bankInvoiceDesc"
                :disabled="disabledEdit"
                type="textarea"
                placeholder="请输入核验结果说明"
                rows="5"
              ></el-input>
            </el-col>
          </el-row>
        </div>
      </div>

      <div class="info-card">
        <span class="card-title">企业申报其他附件</span>
        <div>
          <BaseUpload :businessOtherFiles="businessOtherFiles" isViewMode></BaseUpload>
        </div>
        <div class="verification-info">
          <p>核验结果说明</p>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-input
                v-model="bankOtherFileDesc"
                :disabled="disabledEdit"
                type="textarea"
                placeholder="请输入核验结果说明"
                rows="5"
              ></el-input>
            </el-col>
          </el-row>
        </div>
      </div>

      <div class="info-card">
        <span class="card-title">收付汇登记信息</span>
        <div v-if="!disabledEdit" class="add-btn">
          <el-button type="text" icon="el-icon-plus" @click="handleAdd('paymentReceiptInfos')"
            >新增</el-button
          >
        </div>
        <el-table :data="paymentReceiptInfos" border style="width: 100%" class="table_Header">
          <el-table-column label="结算方向" width="180">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.settleDirection"
                :disabled="disabledEdit"
                placeholder="请选择"
                size="mini"
                style="width: 100%"
              >
                <el-option
                  v-for="item in settlementDirectionParam"
                  :key="item.detailId"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="国际收支申报号码" width="180">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.declareNo"
                :disabled="disabledEdit"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="国际收支交易编码" width="180">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.declareCode"
                :disabled="disabledEdit"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="币种" width="160">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.currencyType"
                :disabled="disabledEdit"
                placeholder="请选择"
                size="mini"
                style="width: 100%"
              >
                <el-option
                  v-for="item in currencyTypeParam"
                  :key="item.detailId"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="金额" width="160">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.amount"
                :disabled="disabledEdit"
                placeholder="请输入"
                size="mini"
                type="number"
              ></el-input>
            </template>
          </el-table-column>

          <el-table-column label="日期" width="190">
            <template slot-scope="scope">
              <el-date-picker
                v-model="scope.row.date"
                :disabled="disabledEdit"
                type="date"
                placeholder="请选择"
                size="mini"
                style="width: 100%"
                value-format="yyyy-MM-dd"
              ></el-date-picker>
            </template>
          </el-table-column>

          <el-table-column label="合同编号" width="160">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.contractNo"
                :disabled="disabledEdit"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="物流单据签注" width="160">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.logisticsEndorsement"
                :disabled="disabledEdit"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="备注" width="180">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.remark"
                :disabled="disabledEdit"
                placeholder="请输入"
                size="mini"
              ></el-input>
            </template>
          </el-table-column>

          <el-table-column label="附件" width="160">
            <template slot-scope="scope">
              <input
                :ref="'paymentFile' + scope.$index"
                accept=".pdf,.jpg,.png,.jpeg"
                type="file"
                style="display: none"
                @change="(e) => uploadContractFile(scope, 'paymentReceiptInfos', e)"
              />
              <el-button
                v-if="!scope.row.attachments?.[0]?.id"
                :disabled="disabledEdit"
                size="mini"
                type="text"
                @click="uploadContract(scope, 'paymentFile')"
              >
                上传
              </el-button>
              <div v-else class="table-cell-value">
                <img src="@/assets/images/attachment.png" alt="" class="attachment-img" />
                <span class="file-value">{{ scope.row.attachments?.[0]?.filename || '-' }}</span>
                <i
                  v-if="!disabledEdit"
                  class="el-icon-close"
                  @click="deleteAttachment(scope, 'paymentReceiptInfos')"
                ></i>
              </div>
            </template>
          </el-table-column>
          <el-table-column v-if="!disabledEdit" label="操作" width="80" fixed="right">
            <template slot-scope="scope">
              <img
                v-if="scope.$index === 0"
                class="action-img"
                src="@/assets/images/delete-white.png"
                alt=""
              />
              <img
                v-else
                :disabled="disabledEdit"
                src="@/assets/images/delete.png"
                class="action-img"
                alt="删除"
                @click="handleDelete(scope, 'paymentReceiptInfos')"
              />
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="info-card">
        <span class="card-title">其他附件</span>
        <BaseUpload
          :businessOtherFiles="bankOtherFiles"
          :isViewMode="disabledEdit"
          @changeFileList="changeFileList"
          @removeFile="removeFile"
        ></BaseUpload>
      </div>
    </div>
  </div>
</template>

<script>
import { getBusinessDataApi, bankTradeSubmitApi } from '@/apis/index'
import { mapGetters } from 'vuex'
import { uploadApi } from '@/apis/upload'
import BaseUpload from '@/component/BaseUpload'

export default {
  name: 'InformationReviewDetail',
  components: { BaseUpload },
  data() {
    return {
      id: '',
      detailData: {},
      contractInfos: [],
      logisticsInfos: [],
      invoiceInfos: [],
      paymentReceiptInfos: [
        {
          settleDirection: '',
          declareNo: '', // 国际收支申报号码
          declareCode: '', //国际收支交易编码
          currencyType: '', // 币种
          amount: '', // 金额
          date: '', // 日期
          contractNo: '', // 合同号
          logisticsEndorsement: '', // 物流单据签注
          remark: '',
          attachments: []
        }
      ],
      businessOtherFiles: [],
      bankContractDesc: '', // 合同核验说明
      bankLogisticsDesc: '', // 物流核验说明
      bankInvoiceDesc: '', // 发票核验说明
      bankOtherFileDesc: '', // 企业申报其他附件核验说明
      bankOtherFiles: [],
      mode: 'view'
    }
  },
  computed: {
    ...mapGetters(['currencyTypeParam', 'contractTypeParam', 'settlementDirectionParam']),
    disabledEdit() {
      return this.mode === 'view'
    }
  },
  created() {
    this.id = this.$route.params.id
    this.mode = this.$route.query.mode || 'view'
    this.$dispatch('enum/getCurrencyTypeParam')
    this.$dispatch('enum/getContractTypeParam')
    this.$dispatch('enum/getSettlementDirectionParam')
    this.loadDetailData()
  },
  methods: {
    loadDetailData() {
      getBusinessDataApi(this.id).then((res) => {
        if (res.code === 200) {
          console.log(res)
          const { data } = res
          this.detailData = {
            ...this.detailData,
            targetBankId: data.targetBankInfo.companyName,
            id: data.targetBankId,
            creditCode: data.targetBankInfo.creditCode,
            financialInstitutionCode: data.targetBankInfo.financialInstitutionCode,
            reportNo: data.reportNo,
            auditStatus: data.auditStatus
          }
          this.businessOtherFiles = data.businessOtherFiles?.map((e) => {
            return { name: e.filename, ...e }
          })
          if (data.bankOtherFiles) {
            this.bankOtherFiles = data.bankOtherFiles?.map((e) => {
              return { name: e.filename, ...e }
            })
          }
          if (data.paymentReceiptInfos) {
            this.paymentReceiptInfos = data.paymentReceiptInfos
          }
          this.contractInfos = data.contractInfos
          this.logisticsInfos = data.logisticsInfos
          this.invoiceInfos = data.invoiceInfos
          this.bankContractDesc = data.bankContractDesc
          this.bankInvoiceDesc = data.bankInvoiceDesc
          this.bankLogisticsDesc = data.bankLogisticsDesc
          this.bankOtherFileDesc = data.bankOtherFileDesc
        }
      })
    },
    changeFileList(data) {
      this.bankOtherFiles.push(data)
    },
    removeFile(fileList) {
      this.bankOtherFiles = fileList
    },
    goBack() {
      this.$router.go(-1)
    },
    uploadContract(scope, refStr) {
      this.$refs[`${refStr}${scope.$index}`].click()
    },
    uploadContractFile(scope, str, e) {
      const file = e.target.files[0]
      if (!file) return
      // 文件类型校验
      const validTypes = ['application/pdf', 'image/jpg', 'image/jpeg', 'image/png']
      const validExtensions = ['.pdf', '.jpg', '.jpeg', '.png']

      // 获取文件扩展名
      const fileName = file.name.toLowerCase()
      const fileExtension = fileName.substring(fileName.lastIndexOf('.'))

      if (!validTypes.includes(file.type) || !validExtensions.includes(fileExtension)) {
        this.$message.error('只能上传PDF、JPG、PNG格式的文件')
        e.target.value = '' // 清空文件选择
        return
      }
      const formData = new FormData()
      formData.append('file', file)
      formData.append('isTemp', false)
      uploadApi(formData).then((res) => {
        this.$set(this[str][scope.$index], 'attachments', [res.data])
      })
    },
    // 删除表格行的附件
    deleteAttachment(scope, str) {
      this.$set(this[str][scope.$index], 'attachments', {})
    },
    customUpload(file) {
      const { file: rawFile } = file

      // 文件类型校验
      const validTypes = ['application/pdf', 'image/jpg', 'image/jpeg', 'image/png']
      const validExtensions = ['.pdf', '.jpg', '.jpeg', '.png']
      const fileName = rawFile.name.toLowerCase()
      const fileExtension = fileName.substring(fileName.lastIndexOf('.'))

      if (!validTypes.includes(rawFile.type) || !validExtensions.includes(fileExtension)) {
        this.$message.error('只能上传PDF、JPG、PNG格式的文件')
        return
      }

      const formData = new FormData()
      formData.append('file', rawFile)
      formData.append('isTemp', false)

      uploadApi(formData)
        .then((res) => {
          this.bankOtherFiles.push({ ...res.data, name: res.data.filename })
          this.$message.success('上传成功')
        })
        .catch(() => {
          this.$message.error('上传失败')
        })
    },
    handleAdd() {
      this.paymentReceiptInfos.push({
        settleDirection: '',
        declareNo: '', // 国际收支申报号码
        declareCode: '', //国际收支交易编码
        currencyType: '', // 币种
        amount: '', // 金额
        date: '', // 日期
        contractNo: '', // 合同号
        logisticsEndorsement: '', // 物流单据签注
        remark: '',
        attachments: []
      })
    },
    submitHandler(actionType) {
      const data = {
        id: this.id,
        action: actionType,
        bankContractDesc: this.bankContractDesc,
        bankLogisticsDesc: this.bankLogisticsDesc,
        bankInvoiceDesc: this.bankInvoiceDesc,
        bankOtherFileDesc: this.bankOtherFileDesc,
        paymentReceiptInfos: this.paymentReceiptInfos.map((e) => {
          return { ...e, date: e.date + ' 00:00:00' }
        }),
        bankOtherFiles: this.bankOtherFiles
      }
      bankTradeSubmitApi(data).then((res) => {
        if (res.code === 200) {
          this.$message({
            message: '核验成功',
            type: 'success'
          })
          this.goBack()
        }
      })
    },
    handleDelete(scope, str) {
      this[str].splice(scope.$index, 1)
    }
  }
}
</script>

<style lang="scss" scoped>
.trade-detail-container {
  padding: 20px;
  background-color: #fff;
  margin-top: 20px;
}

.page-header {
  border-bottom: 1px solid #ededed;
  margin: 0 -20px 20px;
  padding: 0 20px 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .back-btn {
    font-size: 20px;
    height: 34px;
    display: flex;
    align-items: center;
    .status-box {
      margin-left: 20px;
      font-size: 14px;
      display: inline-block;
      height: 100%;
      line-height: 34px;
      padding: 0 10px;
      border-radius: 4px;
      background-color: #f5faff;
      color: #1659ce;
    }
  }
}

.info-card {
  margin-bottom: 20px;

  .card-title {
    display: inline-block;
    font-size: 18px;
    border-left: 3px solid #3566f4;
    font-weight: bold;
    color: #17233d;
    padding-left: 5px;
    margin-bottom: 30px;
  }
  .verification-info {
    margin-top: 30px;
    p {
      color: #17233d;
      font-size: 14px;
      margin-bottom: 10px;
    }
  }
}

.detail-form {
  .form-value {
    color: #606266;
    line-height: 40px;
    display: inline-block;
    min-height: 40px;
    padding: 0 15px;
    background-color: #f5f7fa;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    width: 100%;
    box-sizing: border-box;
  }

  .el-input,
  .el-select {
    width: 100%;
  }
}

.footer-actions {
  text-align: center;
  .el-button {
    margin: 0 10px;
    min-width: 100px;
  }
}

.table-cell-value {
  display: inline-block;
  padding: 0 8px;
  border-radius: 4px;
  width: 100%;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  .attachment-img {
    width: 16px;
    height: 16px;
  }
  .file-value {
    display: inline-block;
    color: #3566f4;
    width: 80%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .el-icon-close {
    cursor: pointer;
  }
}

::v-deep .div__header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
}

::v-deep .el-table {
  .el-button--text {
    color: #409eff;

    &:hover {
      color: #66b1ff;
    }
  }

  .el-table__cell {
    padding: 8px 0;
  }

  .el-input--mini .el-input__inner,
  .el-select--mini .el-input__inner,
  .el-date-editor--mini .el-input__inner {
    height: 32px;
    line-height: 32px;
  }
}
::v-deep {
  .el-upload-dragger,
  .el-upload {
    width: 100%;
  }
  .el-upload-dragger {
    display: flex;
    justify-content: center;
    align-content: center;
    flex-direction: column;
    .el-upload__text {
      font-weight: bold;
      font-size: 20px;
      margin-bottom: 20px;
    }
  }
}
.add-btn {
  display: flex;
  justify-content: flex-end;
}
.action-img {
  width: 24px;
  height: 24px;
}
.verification-info {
  margin-top: 30px;
  p {
    font-size: 14px;
    margin-bottom: 10px;
  }
}
</style>
