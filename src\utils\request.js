import axios from 'axios'
import Cookies from 'js-cookie'
import { Message, Loading } from 'element-ui'
import store from '@/store'
import router from '@/router'

// 创建自定义 axios 实例
console.log(process.env.VUE_APP_BASE_URL, 'VUE_APP_BASE_URL')

const apiClient = axios.create({
  baseURL: process.env.VUE_APP_BASE_URL,
  headers: {
    'Content-Type': 'application/json'
    // Accept: 'application/json'
  },
  timeout: 60000
})
// 用于存储 loading 实例
let loadingInstance = null
// 添加请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    if (!config.noLoading) {
      loadingInstance = Loading.service({
        lock: true,
        text: '加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
    }

    // 在这里可以添加认证令牌
    const token = Cookies.get('AMS-SIMPLE-ADMIN-TRADE-TOKEN')
    if (token) {
      config.headers['Authorization'] = decodeURIComponent(token)
    }
    return config
  },
  (error) => {
    // 关闭 loading
    if (loadingInstance) {
      loadingInstance.close()
    }
    return Promise.reject(error)
  }
)

// 添加响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    // console.log(response, 'error')
    // 关闭 loading
    if (loadingInstance) {
      loadingInstance.close()
    }
    // 处理二进制数据响应
    const contentType = response.headers['content-type']
    if (
      response.config.responseType === 'blob' ||
      contentType?.includes('application/octet-stream') ||
      contentType?.includes('application/pdf')
    ) {
      return response.data
    }
    const status = response.status
    if (status < 200 || status > 300) {
      if (response.message) {
        Message.error(response.message)
      } else {
        Message.error('服务器内部错误')
      }

      return Promise.reject('error')
    } else {
      const data = response.data
      if (data?.code === 401) {
        store.dispatch('auth/logout').then(() => {
          router.push('/login')
        })
        return Promise.reject('error')
      }
      if (data?.code !== 200) {
        Message.error(data.msg)
      }
    }
    return response.data
  },
  (error) => {
    // 全局处理错误
    if (loadingInstance) {
      loadingInstance.close()
    }
    if (error.response) {
      // 服务器返回了 2xx 范围之外的状态码
      console.log('API 错误:', error.response.status, error.response.data)
      switch (error.response.status) {
        case 401:
          // 重定向到登录页或刷新令牌
          store.dispatch('auth/logout').then(() => {
            router.push('/login')
          })
          console.log('未授权，正在重定向到登录页...')
          break
        case 500:
          // 处理 500 错误
          Message.error('服务器内部错误')
          break
        default:
          // 其他错误状态码的处理
          Message.error('其他错误状态码')
      }
    } else if (error.request) {
      // 请求已发出，但没有收到响应
      console.error('未收到响应:', error.request)
    } else {
      // 设置请求时发生了错误
      console.error('请求错误:', error.message)
    }
    return Promise.reject(error)
  }
)

export default apiClient
