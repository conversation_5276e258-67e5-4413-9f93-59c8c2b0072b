import apiClient from '@/utils/request'

//业务状态
export function getTradeStatusParamApi() {
  return apiClient.get('/system/api/dict/trade_business_status')
}

// 合同类型
export function getContractTypeParamApi() {
  return apiClient.get('/system/api/dict/contract_type')
}

// 合同类型
export function getCurrencyTypeParamApi() {
  return apiClient.get('/system/api/dict/currency_type')
}

//字典-企业类型
export function dictEnterpriseType() {
  return apiClient.get('/system/api/dict/enterprise_type')
}

//字典-银行类型
export function dictBankType() {
  return apiClient.get('/system/api/dict/bank_type')
}

// 结算方向
export function getSettlementDirectionParamApi() {
  return apiClient.get('/system/api/dict/settlement_direction')
}

// 账号类型
export function dictOrganType() {
  return apiClient.get('/system/api/dict/organ_type')
}